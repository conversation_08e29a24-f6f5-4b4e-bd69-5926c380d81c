test:
	python test.py

download_crypto:
	python scripts/ccxt/download_during.py \
    --exchange binance \
    --symbol WLD/USDT:USDT \
    --start_date 2025-04-01 \
    --end_date 2025-04-15 \
    --output k_data/WLD_USDT_USDT.csv

download_us:
	python scripts/longport_download_during.py

live:
	python scripts/live.py

# 新的统一回测系统
# 机器学习分类回测
bt_lgbm_classify:
	python backtest/backtest_ml_classify.py --model lgbm_classify

# 机器学习回归回测
bt_lgbm_regression:
	python backtest/backtest_ml_regression.py --model lgbm_regression

# 深度学习分类回测
bt_trm_classify:
	python backtest/backtest_dl_classify.py --model transformer_classify

bt_lstm_classify:
	python backtest/backtest_dl_classify.py --model lstm_classify

# 深度学习回归回测（占位符，需要进一步实现）
bt_trm_regression:
	python backtest/backtest_dl_regression.py --model transformer_regression

bt_lstm_regression:
	python backtest/backtest_dl_regression.py --model lstm_regression

# 兼容性：保留原有命令（已弃用）
bt_trm:
	@echo "⚠️ 警告: bt_trm 已弃用，请使用 bt_trm_classify"
	python models/trm/trmV2.py

analysis:
	python feature_importance_analysis.py

# 批量回测命令
bt_all_ml:
	@echo "🚀 运行所有机器学习模型回测..."
	make bt_lgbm_classify
	make bt_lgbm_regression

bt_all_dl:
	@echo "🚀 运行所有深度学习模型回测..."
	make bt_trm_classify
	make bt_lstm_classify

bt_all:
	@echo "🚀 运行所有模型回测..."
	make bt_all_ml
	make bt_all_dl

# 帮助信息
help:
	@echo "📋 可用的回测命令:"
	@echo ""
	@echo "机器学习模型:"
	@echo "  bt_lgbm_classify    - LightGBM分类模型回测"
	@echo "  bt_lgbm_regression  - LightGBM回归模型回测"
	@echo ""
	@echo "深度学习模型:"
	@echo "  bt_trm_classify     - Transformer分类模型回测"
	@echo "  bt_lstm_classify    - LSTM分类模型回测"
	@echo "  bt_trm_regression   - Transformer回归模型回测 (占位符)"
	@echo "  bt_lstm_regression  - LSTM回归模型回测 (占位符)"
	@echo ""
	@echo "批量回测:"
	@echo "  bt_all_ml          - 运行所有机器学习模型"
	@echo "  bt_all_dl          - 运行所有深度学习模型"
	@echo "  bt_all             - 运行所有模型"
	@echo ""
	@echo "其他:"
	@echo "  analysis           - 特征重要性分析"
	@echo "  help               - 显示此帮助信息"
