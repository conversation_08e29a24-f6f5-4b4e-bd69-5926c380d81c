#!/usr/bin/env python3
"""
中文字体配置工具
解决matplotlib中文乱码问题
"""

import matplotlib
import matplotlib.pyplot as plt
import platform
import os


def setup_chinese_font():
    """
    设置中文字体以解决matplotlib乱码问题
    
    根据不同操作系统自动选择合适的中文字体
    """
    system = platform.system()
    
    # 根据操作系统定义字体优先级列表
    if system == "Darwin":  # macOS
        font_list = [
            'Arial Unicode MS',  # macOS默认支持中文的字体
            'PingFang SC',       # 苹方字体
            'STHeiti',           # 华文黑体
            'Hiragino Sans GB',  # 冬青黑体
            'SimHei',            # 黑体
            'Heiti TC'           # 黑体繁体
        ]
    elif system == "Windows":  # Windows
        font_list = [
            'Microsoft YaHei',   # 微软雅黑
            'SimHei',            # 黑体
            'SimSun',            # 宋体
            'KaiTi',             # 楷体
            'FangSong',          # 仿宋
            'Microsoft JhengHei' # 微软正黑体
        ]
    else:  # Linux
        font_list = [
            'Noto Sans CJK SC',      # Google Noto字体
            'WenQuanYi Micro Hei',   # 文泉驿微米黑
            'WenQuanYi Zen Hei',     # 文泉驿正黑
            'SimHei',                # 黑体
            'DejaVu Sans'            # Linux默认字体
        ]
    
    # 尝试设置字体
    font_found = False
    for font in font_list:
        try:
            # 测试字体是否可用
            test_fig, test_ax = plt.subplots(figsize=(1, 1))
            test_ax.text(0.5, 0.5, '测试中文', fontfamily=font, fontsize=12)
            plt.close(test_fig)
            
            # 如果没有报错，设置为默认字体
            matplotlib.rcParams['font.sans-serif'] = [font]
            matplotlib.rcParams['font.family'] = 'sans-serif'
            matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            print(f"✅ 已设置中文字体: {font}")
            font_found = True
            break
        except Exception as e:
            continue
    
    if not font_found:
        print("⚠️  警告: 未找到合适的中文字体，可能会出现中文乱码")
        print("建议安装以下字体之一:")
        if system == "Darwin":
            print("  - Arial Unicode MS (通常已预装)")
            print("  - PingFang SC (通常已预装)")
        elif system == "Windows":
            print("  - SimHei (黑体)")
            print("  - Microsoft YaHei (微软雅黑)")
        else:
            print("  - sudo apt-get install fonts-wqy-microhei  # Ubuntu/Debian")
            print("  - sudo yum install wqy-microhei-fonts     # CentOS/RHEL")


def get_chinese_font():
    """
    获取当前设置的中文字体名称
    
    Returns:
        str: 字体名称，如果未设置则返回默认字体
    """
    font_list = matplotlib.rcParams['font.sans-serif']
    return font_list[0] if font_list else 'default'


def test_chinese_display():
    """
    测试中文显示效果
    """
    print("测试中文显示效果...")
    
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 测试各种中文文本
    test_texts = [
        "标题: LightGBM回归模型测试",
        "横轴: 真实收益率",
        "纵轴: 预测收益率", 
        "图例: 训练数据",
        "负数测试: -0.123",
        "百分比: 85.6%"
    ]
    
    for i, text in enumerate(test_texts):
        ax.text(0.1, 0.9 - i*0.12, text, fontsize=12, transform=ax.transAxes)
    
    ax.set_title("中文字体显示测试", fontsize=16)
    ax.set_xlabel("X轴标签", fontsize=14)
    ax.set_ylabel("Y轴标签", fontsize=14)
    
    # 隐藏坐标轴
    ax.set_xticks([])
    ax.set_yticks([])
    
    plt.tight_layout()
    plt.savefig('./chinese_font_test.png', dpi=150, bbox_inches='tight')
    print("中文字体测试图已保存到: ./chinese_font_test.png")
    plt.show()


if __name__ == "__main__":
    print("=== 中文字体配置工具 ===")
    setup_chinese_font()
    print(f"当前字体: {get_chinese_font()}")
    
    # 运行测试
    test_chinese_display()
