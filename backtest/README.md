# 统一回测系统

这是一个重构后的统一回测系统，支持机器学习和深度学习模型的分类和回归任务。

## 🏗️ 架构设计

```
backtest/
├── __init__.py
├── README.md                    # 本文档
├── backtest_ml_classify.py      # 机器学习分类回测
├── backtest_ml_regression.py    # 机器学习回归回测
├── backtest_dl_classify.py      # 深度学习分类回测
└── backtest_dl_regression.py    # 深度学习回归回测

common/
├── __init__.py
├── globals.py                   # 全局配置和模型注册表
└── base_backtest.py            # 通用回测基类
```

## 🚀 快速开始

### 使用 Makefile（推荐）

```bash
# 查看所有可用命令
make help

# 运行 LightGBM 分类回测
make bt_lgbm_classify

# 运行 LightGBM 回归回测
make bt_lgbm_regression

# 运行 Transformer 分类回测
make bt_trm_classify

# 批量运行所有机器学习模型
make bt_all_ml

# 批量运行所有模型
make bt_all
```

### 直接使用 Python

```bash
# 机器学习分类
python backtest/backtest_ml_classify.py --model lgbm_classify --data ./k_data/AAPL_US_5min.csv

# 机器学习回归
python backtest/backtest_ml_regression.py --model lgbm_regression --data ./k_data/AAPL_US_5min.csv

# 深度学习分类
python backtest/backtest_dl_classify.py --model transformer_classify --data ./k_data/AAPL_US_5min.csv
```

## 📊 支持的模型

### 机器学习模型

| 模型名称 | 类型 | 状态 | 描述 |
|---------|------|------|------|
| `lgbm_classify` | 分类 | ✅ 完成 | LightGBM分类模型 |
| `lgbm_regression` | 回归 | ✅ 完成 | LightGBM回归模型 |

### 深度学习模型

| 模型名称 | 类型 | 状态 | 描述 |
|---------|------|------|------|
| `transformer_classify` | 分类 | ✅ 完成 | Transformer分类模型 |
| `lstm_classify` | 分类 | ⚠️ 待测试 | LSTM分类模型 |
| `transformer_regression` | 回归 | 🚧 占位符 | Transformer回归模型 |
| `lstm_regression` | 回归 | 🚧 占位符 | LSTM回归模型 |

## ⚙️ 配置选项

### 通用参数

- `--model`: 模型名称（必需）
- `--data`: 数据文件路径
- `--batch_size`: 训练批次大小
- `--validation_size`: 验证批次大小
- `--window`: 滚动标准化窗口大小
- `--no_plot`: 不绘制结果图表
- `--no_save`: 不保存详细结果

### 机器学习特有参数

- `--no_balance`: 不进行类别平衡（仅分类）

### 深度学习特有参数

- `--seq_len`: 序列长度
- `--epochs`: 训练轮数
- `--learning_rate`: 学习率

## 📁 输出文件

回测结果会保存在以下位置：

- **模型文件**: `./models_saved/{model_name}_{data_name}.pkl`
- **详细结果**: `./results/{model_name}_{data_name}_results.csv`
- **结果图表**: `./results/{model_name}_{data_name}_plot.png`

## 🔧 添加新模型

### 1. 在 `common/globals.py` 中注册模型

```python
# 添加到对应的模型注册表
ML_MODELS['new_model'] = {
    'module': 'models.new_model.new_model',
    'class': 'NewModel',
    'type': 'classification',  # 或 'regression'
    'data_type': 'ml',  # 或 'dl'
    'default_params': {
        'param1': 'value1',
        'param2': 'value2'
    }
}
```

### 2. 确保模型类实现必要的接口

模型类需要实现以下方法：
- `prepare_data(csv_file, **kwargs)`
- `train_model(X_train, y_train, **kwargs)`
- `predict(X)`
- `rolling_train_validate(X, y, dates)`
- `save_model(path)`
- `load_model(path)`

### 3. 在 Makefile 中添加命令

```makefile
bt_new_model:
	python backtest/backtest_ml_classify.py --model new_model
```

## 🐛 故障排除

### 常见问题

1. **模型不存在错误**
   - 检查模型名称是否在 `common/globals.py` 中注册
   - 确认模型类型（分类/回归）和数据类型（ML/DL）匹配

2. **导入错误**
   - 确保模型文件路径正确
   - 检查模型类名是否正确

3. **GPU 相关错误**
   - 深度学习模型会自动检测GPU可用性
   - 如果GPU内存不足，会自动回退到CPU

### 调试技巧

- 使用 `--no_plot --no_save` 参数加快调试速度
- 检查 `common/globals.py` 中的模型配置
- 查看详细的错误堆栈信息

## 📈 性能优化建议

1. **数据预处理**
   - 适当调整 `window` 参数
   - 对于大数据集，考虑增加 `batch_size`

2. **模型训练**
   - 机器学习模型：调整树的参数（`num_leaves`, `n_estimators`）
   - 深度学习模型：调整网络结构参数（`model_dim`, `num_layers`）

3. **硬件利用**
   - 深度学习模型优先使用GPU
   - 机器学习模型可以利用多核CPU

## 🔄 迁移指南

从旧的回测系统迁移：

1. **替换 Makefile 命令**
   ```bash
   # 旧命令
   make bt_trm
   
   # 新命令
   make bt_trm_classify
   ```

2. **更新脚本调用**
   ```bash
   # 旧方式
   python models/lightgbm/lgbm_classify.py
   
   # 新方式
   python backtest/backtest_ml_classify.py --model lgbm_classify
   ```

3. **配置参数调整**
   - 大部分参数保持兼容
   - 新增了更多灵活的配置选项
