"""
机器学习回归模型回测
支持 LightGBM、XGBoost 等机器学习回归模型的滚动回测
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.base_backtest import BaseBacktest
from common.globals import BacktestConfig, get_models_by_type, get_models_by_data_type


class MLRegressionBacktest(BaseBacktest):
    """机器学习回归回测类"""
    
    def get_expected_model_type(self) -> str:
        return 'regression'
    
    def get_expected_data_type(self) -> str:
        return 'ml'
    
    def prepare_data(self, csv_file: str) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备机器学习回归数据
        
        Args:
            csv_file: 数据文件路径
            
        Returns:
            X: 特征数据 [样本数, 特征数]
            y: 目标收益率 [样本数]
            dates: 日期列表
        """
        print("📊 准备机器学习回归数据...")
        
        # 调用模型的数据准备方法
        X, y, dates = self.model.prepare_data(
            csv_file, 
            window=self.config.window
        )
        
        print(f"✅ 数据准备完成:")
        print(f"   特征形状: {X.shape}")
        print(f"   目标形状: {y.shape}")
        print(f"   日期数量: {len(dates)}")
        
        # 统计目标变量分布
        print(f"   目标统计:")
        print(f"     均值: {y.mean():.6f}")
        print(f"     标准差: {y.std():.6f}")
        print(f"     最小值: {y.min():.6f}")
        print(f"     最大值: {y.max():.6f}")
        
        # 统计收益率分布
        positive_count = np.sum(y > 0)
        negative_count = np.sum(y < 0)
        zero_count = np.sum(y == 0)
        total = len(y)
        
        print(f"   收益率分布:")
        print(f"     正收益: {positive_count} ({positive_count/total*100:.1f}%)")
        print(f"     负收益: {negative_count} ({negative_count/total*100:.1f}%)")
        print(f"     零收益: {zero_count} ({zero_count/total*100:.1f}%)")
        
        return X, y, dates
    
    def evaluate_predictions(self, y_true, y_pred, **kwargs) -> Dict[str, float]:
        """
        评估回归预测结果
        
        Args:
            y_true: 真实收益率
            y_pred: 预测收益率
            **kwargs: 其他参数
            
        Returns:
            评估指标字典
        """
        # 基本回归指标
        mse = mean_squared_error(y_true, y_pred)
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)
        
        # 方向准确率（预测涨跌方向是否正确）
        direction_accuracy = self._calculate_direction_accuracy(y_true, y_pred)
        
        # 强趋势准确率（对于较大的收益率变化，预测方向是否正确）
        strong_trend_accuracy = self._calculate_strong_trend_accuracy(y_true, y_pred)
        
        # 预测值统计
        pred_mean = np.mean(y_pred)
        pred_std = np.std(y_pred)
        
        metrics = {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'r2_score': r2,
            'direction_accuracy': direction_accuracy,
            'strong_trend_accuracy': strong_trend_accuracy,
            'pred_mean': pred_mean,
            'pred_std': pred_std
        }
        
        return metrics
    
    def _calculate_direction_accuracy(self, y_true, y_pred) -> float:
        """计算方向准确率"""
        true_direction = np.sign(y_true)
        pred_direction = np.sign(y_pred)
        
        # 排除零值
        non_zero_mask = (true_direction != 0) & (pred_direction != 0)
        if np.sum(non_zero_mask) == 0:
            return 0.0
        
        correct_direction = true_direction[non_zero_mask] == pred_direction[non_zero_mask]
        return np.mean(correct_direction)
    
    def _calculate_strong_trend_accuracy(self, y_true, y_pred, threshold=0.02) -> float:
        """计算强趋势准确率（对于绝对值大于阈值的收益率）"""
        strong_trend_mask = np.abs(y_true) > threshold
        if np.sum(strong_trend_mask) == 0:
            return 0.0
        
        true_strong = y_true[strong_trend_mask]
        pred_strong = y_pred[strong_trend_mask]
        
        true_direction = np.sign(true_strong)
        pred_direction = np.sign(pred_strong)
        
        correct_direction = true_direction == pred_direction
        return np.mean(correct_direction)
    
    def print_prediction_analysis(self, y_true, y_pred):
        """打印预测分析"""
        print("\n📊 预测分析:")
        
        # 分段分析
        thresholds = [0.005, 0.01, 0.02, 0.05]
        
        for threshold in thresholds:
            mask = np.abs(y_true) > threshold
            if np.sum(mask) > 0:
                segment_true = y_true[mask]
                segment_pred = y_pred[mask]
                
                direction_acc = self._calculate_direction_accuracy(segment_true, segment_pred)
                rmse = np.sqrt(mean_squared_error(segment_true, segment_pred))
                
                print(f"   |收益率| > {threshold:.1%}: {np.sum(mask)} 样本")
                print(f"     方向准确率: {direction_acc:.1%}")
                print(f"     RMSE: {rmse:.6f}")
        
        # 预测偏差分析
        bias = np.mean(y_pred - y_true)
        print(f"\n   预测偏差: {bias:.6f}")
        
        if bias > 0.001:
            print("     ⚠️ 模型倾向于过度预测上涨")
        elif bias < -0.001:
            print("     ⚠️ 模型倾向于过度预测下跌")
        else:
            print("     ✅ 预测相对无偏")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='机器学习回归模型回测')
    parser.add_argument('--model', type=str, required=True, 
                       help='模型名称 (如: lgbm_regression)')
    parser.add_argument('--data', type=str, 
                       help='数据文件路径')
    parser.add_argument('--batch_size', type=int, default=500,
                       help='训练批次大小')
    parser.add_argument('--validation_size', type=int, default=100,
                       help='验证批次大小')
    parser.add_argument('--window', type=int, default=200,
                       help='滚动标准化窗口大小')
    parser.add_argument('--no_plot', action='store_true',
                       help='不绘制结果图表')
    parser.add_argument('--no_save', action='store_true',
                       help='不保存详细结果')
    
    args = parser.parse_args()
    
    # 验证模型是否支持
    ml_regression_models = get_models_by_type('regression')
    ml_regression_models = {k: v for k, v in ml_regression_models.items() 
                           if v['data_type'] == 'ml'}
    
    if args.model not in ml_regression_models:
        print(f"❌ 不支持的模型: {args.model}")
        print(f"支持的机器学习回归模型: {list(ml_regression_models.keys())}")
        return
    
    # 创建配置
    config = BacktestConfig(
        csv_file=args.data or './k_data/AAPL_US_5min.csv',
        batch_size=args.batch_size,
        validation_size=args.validation_size,
        window=args.window,
        balance_classes=False,  # 回归任务不需要类别平衡
        plot_results=not args.no_plot,
        save_detailed_results=not args.no_save
    )
    
    # 创建回测器
    backtest = MLRegressionBacktest(config)
    
    try:
        # 运行回测
        results = backtest.run_backtest(args.model)
        
        # 打印总结
        backtest.print_summary(results)
        
        # 如果有预测结果，打印预测分析
        if 'predictions' in results and 'true_values' in results:
            backtest.print_prediction_analysis(results['true_values'], results['predictions'])
        
        print("\n✅ 回测完成!")
        
    except Exception as e:
        print(f"❌ 回测失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
