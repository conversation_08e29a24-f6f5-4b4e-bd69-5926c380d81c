"""
这个版本反标准化为价格还有问题
"""
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import matplotlib.pyplot as plt

# 读取数据
filepath = './sample_data/THE_USDT.csv'
df = pd.read_csv(filepath)

# 构造特征
features = pd.DataFrame()
features['Return'] = np.log(df['close'] / (df['close'].shift(1) + 1e-8))  
features['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
features['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
features.replace([np.inf, -np.inf], np.nan, inplace=True)
features = features.dropna()

# 归一化
def normalize_data(df):
    normalized_df = pd.DataFrame()
    for column in df.columns:
        if column == 'Return':  # 归一化到[-1, 1]
            values = df[column].values
			# 涨跌20% [-0.23, 0.19)
            normalized_dim = np.zeros_like(values)
            # 正数除以0.19, 这里不限制最大为1.0, 因为涨跌极其偶尔会超过20%也不影响模型
            positive_mask = values > 0
            normalized_dim[positive_mask] = values[positive_mask] / 0.19
            # 负数除以0.23
            negative_mask = values < 0
            normalized_dim[negative_mask] = values[negative_mask] / 0.23
            # 0值保持不变
            normalized_df[column] = normalized_dim.ravel()
        elif column == 'BuyPower':  # 归一化到[-1, 1]
            values = df[column].values  # 这里的范围是[-0.5, 0.5]
            normalized_df[column] = values * 2
        elif column == 'IBS':  # 归一化到[-1, 1]? 好像没必要
            values = df[column].values  # 这里的范围是[0, 1]
            normalized_df[column] = values
    return normalized_df

normalized_features = normalize_data(features)

train_set_size, test_set_size = 0, 0
def split_data(data, lookback):
    global train_set_size, test_set_size
    data_raw = data.to_numpy() # convert to numpy array
    data_seq = []
    
    # create all possible sequences of length seq_len
    for index in range(len(data_raw) - lookback): 
        data_seq.append(data_raw[index: index + lookback])
    
    data_seq = np.array(data_seq)
    test_set_size = 1000
    train_set_size = data_seq.shape[0] - test_set_size
    
    return_idx = data.columns.get_loc('Return')
    x_train = data_seq[:train_set_size, :-1, :]
    y_train = data_seq[:train_set_size, -1, return_idx]
    
    x_test = data_seq[train_set_size:, :-1, :]
    y_test = data_seq[train_set_size:, -1, return_idx]
    
    return [x_train, y_train, x_test, y_test]

lookback = 20 # choose sequence length
x_train, y_train, x_test, y_test = split_data(normalized_features, lookback)
print('x_train.shape = ',x_train.shape)
print('y_train.shape = ',y_train.shape)
print('x_test.shape = ',x_test.shape)
print('y_test.shape = ',y_test.shape)

x_train = torch.from_numpy(x_train).type(torch.Tensor)
x_test = torch.from_numpy(x_test).type(torch.Tensor)
y_train_lstm = torch.from_numpy(y_train).type(torch.Tensor)
y_test_lstm = torch.from_numpy(y_test).type(torch.Tensor)

input_dim = len(features.columns)
hidden_dim = 32
num_layers = 2
output_dim = 1
num_epochs = 50

class LSTM(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, output_dim):
        super(LSTM, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()
        out, (hn, cn) = self.lstm(x, (h0.detach(), c0.detach()))
        out = self.fc(out[:, -1, :]) 
        return out
    
model = LSTM(input_dim=input_dim, hidden_dim=hidden_dim, output_dim=output_dim, num_layers=num_layers)
criterion = torch.nn.MSELoss(reduction='mean')
optimiser = torch.optim.Adam(model.parameters(), lr=0.01)

import time

hist = np.zeros(num_epochs)
start_time = time.time()
lstm = []

for t in range(num_epochs):
    y_train_pred = model(x_train)

    loss = criterion(y_train_pred, y_train_lstm)
    print("Epoch:", t, "   LOSS:", loss.item())
    hist[t] = loss.item()

    optimiser.zero_grad()
    loss.backward()
    optimiser.step()
    
training_time = time.time()-start_time
print("Training time: {}".format(training_time))

# 将对数收益率转换为价格
def log_return_to_price(log_returns, initial_price):
    prices = [initial_price]
    for r in log_returns:
        prices.append(prices[-1] * np.exp(r))
    return np.array(prices[1:])

base_price = y_test_lstm[0]
predict = y_train_pred.detach().numpy()
original = y_train_lstm.detach().numpy()
original_prices = log_return_to_price(original.flatten(), base_price)
predicted_prices = log_return_to_price(predict.flatten(), base_price)


import seaborn as sns
sns.set_style("darkgrid")    

fig = plt.figure()
fig.subplots_adjust(hspace=0.2, wspace=0.2)

plt.subplot(1, 2, 1)
ax = sns.lineplot(x = range(len(original_prices)), y = original_prices, label="Data", color='royalblue')
ax = sns.lineplot(x = range(len(predicted_prices)), y = predicted_prices, label="Training Prediction (LSTM)", color='tomato')
ax.set_title('Stock price', size = 14, fontweight='bold')
ax.set_xlabel("Days", size = 14)
ax.set_ylabel("Cost (USD)", size = 14)
ax.set_xticklabels('', size=10)


plt.subplot(1, 2, 2)
ax = sns.lineplot(data=hist, color='royalblue')
ax.set_xlabel("Epoch", size = 14)
ax.set_ylabel("Loss", size = 14)
ax.set_title("Training Loss", size = 14, fontweight='bold')
fig.set_figheight(6)
fig.set_figwidth(16)

plt.savefig('training_results.png')