"""
这个版本修复了:
- 反标准化为价格还有问题
"""
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import matplotlib.pyplot as plt

# 读取数据
filepath = './sample_data/THE_USDT.csv'
df = pd.read_csv(filepath)

# 构造特征
features = pd.DataFrame()
features['BasePrice'] = df['close'].shift(1)
features['Return'] = np.log(df['close'] / (df['close'].shift(1) + 1e-8))  # ln(P(t)/P(t-1))
features['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
features['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
features.replace([np.inf, -np.inf], np.nan, inplace=True)
features = features.dropna()
X = features.drop(columns=['BasePrice'])

# 归一化
def normalize_data(df):
    normalized_df = pd.DataFrame()
    for column in df.columns:
        if column == 'Return':  # 归一化到[-1, 1]
            values = df[column].values
            # 涨跌20% [-0.23, 0.19)
            normalized_dim = np.zeros_like(values)
            # 正数除以0.19, 这里不限制最大为1.0, 因为涨跌极其偶尔会超过20%也不影响模型
            positive_mask = values > 0
            normalized_dim[positive_mask] = values[positive_mask] / 0.19
            # 负数除以0.23
            negative_mask = values < 0
            normalized_dim[negative_mask] = values[negative_mask] / 0.23
            # 0值保持不变
            normalized_df[column] = normalized_dim.ravel()
        elif column == 'BuyPower':  # 归一化到[-1, 1]
            values = df[column].values  # 这里的范围是[-0.5, 0.5]
            normalized_df[column] = values * 2
        elif column == 'IBS':  # 归一化到[-1, 1]? 好像没必要
            values = df[column].values  # 这里的范围是[0, 1]
            normalized_df[column] = values
    return normalized_df

def denormalize_data(normalized_values):
    denormalized_values = np.zeros_like(normalized_values)
    # 对正数进行反标准化
    positive_mask = normalized_values > 0
    denormalized_values[positive_mask] = normalized_values[positive_mask] * 0.19
    # 对负数进行反标准化
    negative_mask = normalized_values < 0
    denormalized_values[negative_mask] = normalized_values[negative_mask] * 0.23
    return denormalized_values


normalized_X = normalize_data(X)

lookback = 20  # seq_len
test_set_size = 1000
train_set_size = len(normalized_X) - lookback - test_set_size

def split_data(data):
    # 这样分割数据好像会导致数据缺失, 每20个数据就缺失了一个
    # 因为我们最后要还原为原价, 必须拿到前一个价格
    data_raw = data.to_numpy() # convert to numpy array
    data_seq = []
    
    for index in range(len(data_raw) - lookback): 
        data_seq.append(data_raw[index: index + lookback])
    
    data_seq = np.array(data_seq)
    return_idx = data.columns.get_loc('Return')
    x_train = data_seq[:train_set_size, :-1, :]
    y_train = data_seq[:train_set_size, -1, return_idx]
    x_test = data_seq[train_set_size:, :-1, :]
    y_test = data_seq[train_set_size:, -1, return_idx]
    return [x_train, y_train, x_test, y_test]

x_train, y_train, x_test, y_test = split_data(normalized_X)
print('x_train.shape = ',x_train.shape)
print('y_train.shape = ',y_train.shape)
print('x_test.shape = ',x_test.shape)
print('y_test.shape = ',y_test.shape)

x_train = torch.from_numpy(x_train).type(torch.Tensor)
y_train = torch.from_numpy(y_train).type(torch.Tensor)
input_dim = len(X.columns)
hidden_dim = 32
num_layers = 2
output_dim = 1
num_epochs = 20

class LSTM(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, output_dim):
        super(LSTM, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()
        out, (hn, cn) = self.lstm(x, (h0.detach(), c0.detach()))
        out = self.fc(out[:, -1, :]) 
        return out.squeeze()


# ---------------------------------------- 训练模型开始 ---------------------------------------- 
model = LSTM(input_dim=input_dim, hidden_dim=hidden_dim, output_dim=output_dim, num_layers=num_layers)
criterion = torch.nn.MSELoss(reduction='mean')
optimiser = torch.optim.Adam(model.parameters(), lr=0.01)
model_path = 'model_lstmV4.pth'

import os, time
start_time = time.time()
if os.path.exists(model_path):
    print('加载模型')
    model.load_state_dict(torch.load(model_path, weights_only=True))
else:
    print('从零训练')
    for t in range(num_epochs):
        y_train_pred = model(x_train)
        loss = criterion(y_train_pred, y_train)
        print("Epoch:", t, "   LOSS:", loss.item())
        optimiser.zero_grad()
        loss.backward()
        optimiser.step()
    
training_time = time.time()-start_time
print("Training time: {}".format(training_time))
torch.save(model.state_dict(), model_path)
# ---------------------------------------- 训练模型完成 ----------------------------------------


# ---------------------------------------- 验证模型开始 ----------------------------------------
# 将对数收益率转换为价格 P(T) = P(T-1) * e^ (ln(P(T)/P(T-1)))
def log_return_to_price(base_prices, ln_earns):
    res = []
    for i, ln_earn in enumerate(ln_earns):
        Pt_1 = base_prices.iloc[i]
        Pt = Pt_1 * np.exp(ln_earn)
        res.append(Pt)
    return np.array(res)
x_test = torch.from_numpy(x_test).type(torch.Tensor)
y_test = torch.from_numpy(y_test).type(torch.Tensor)
base_prices = features['BasePrice'][-test_set_size-1:-1]
normalized_ln_earn = y_test.detach().numpy()  # 这个是归一化后的对数收益率
ln_earns = denormalize_data(normalized_ln_earn)
original_prices = log_return_to_price(base_prices, ln_earns)
original_prices_true = features['BasePrice'][-test_set_size:].values
# ---------------------------------------- 验证模型完成 ----------------------------------------



# ---------------------------------------- 绘制结果 ----------------------------------------
import seaborn as sns
sns.set_style("darkgrid")    

fig = plt.figure()
fig.subplots_adjust(hspace=0.2, wspace=0.2)

plt.subplot(1, 2, 1)
ax = sns.lineplot(x = range(len(original_prices)), y = original_prices, label="testData(Norm)", color='royalblue')
ax.set_title('Stock price', size = 14, fontweight='bold')
ax.set_xlabel("Days", size = 14)
ax.set_ylabel("Price", size = 14)
ax.set_xticklabels('', size=10)


plt.subplot(1, 2, 2)
ax = sns.lineplot(x = range(len(original_prices_true)), y = original_prices_true, label="testData(True)", color='tomato')
ax.set_title("Stock price", size = 14, fontweight='bold')
ax.set_xlabel("Days", size = 14)
ax.set_ylabel("Price", size = 14)
ax.set_xticklabels('', size=10)
fig.set_figheight(6)
fig.set_figwidth(16)
plt.savefig('training_results.png')
# ---------------------------------------- 绘制结果完成 ----------------------------------------