{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["../../sample_data/THE_USDT.csv\n", "../../sample_data/BTC_USDT.csv\n", "../../sample_data/XRP_USDT.csv\n", "../../sample_data/WLD_USDT.csv\n", "../../sample_data/DOGE_USDT.csv\n", "../../sample_data/SOL_USDT.csv\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "import os\n", "for dirname, _, filenames in os.walk('../../sample_data'):\n", "    for filename in filenames:\n", "        print(os.path.join(dirname, filename))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "object", "type": "string"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}, {"name": "buy_volume", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "9d7abd69-a1be-4785-bc84-43e533af2ca5", "rows": [["0", "2024-12-03 03:20:00", "2.5081", "2.5103", "2.4991", "2.5016", "98409.5", "33103.7"], ["1", "2024-12-03 03:21:00", "2.5014", "2.5069", "2.4992", "2.5062", "21917.6", "9541.2"], ["2", "2024-12-03 03:22:00", "2.5056", "2.5087", "2.5", "2.5082", "28555.5", "12828.1"], ["3", "2024-12-03 03:23:00", "2.5082", "2.5082", "2.501", "2.5027", "24897.9", "5087.0"], ["4", "2024-12-03 03:24:00", "2.5028", "2.5095", "2.4976", "2.5068", "41689.5", "24589.2"]], "shape": {"columns": 7, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>buy_volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-12-03 03:20:00</td>\n", "      <td>2.5081</td>\n", "      <td>2.5103</td>\n", "      <td>2.4991</td>\n", "      <td>2.5016</td>\n", "      <td>98409.5</td>\n", "      <td>33103.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-12-03 03:21:00</td>\n", "      <td>2.5014</td>\n", "      <td>2.5069</td>\n", "      <td>2.4992</td>\n", "      <td>2.5062</td>\n", "      <td>21917.6</td>\n", "      <td>9541.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-12-03 03:22:00</td>\n", "      <td>2.5056</td>\n", "      <td>2.5087</td>\n", "      <td>2.5000</td>\n", "      <td>2.5082</td>\n", "      <td>28555.5</td>\n", "      <td>12828.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-12-03 03:23:00</td>\n", "      <td>2.5082</td>\n", "      <td>2.5082</td>\n", "      <td>2.5010</td>\n", "      <td>2.5027</td>\n", "      <td>24897.9</td>\n", "      <td>5087.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-03 03:24:00</td>\n", "      <td>2.5028</td>\n", "      <td>2.5095</td>\n", "      <td>2.4976</td>\n", "      <td>2.5068</td>\n", "      <td>41689.5</td>\n", "      <td>24589.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             timestamp    open    high     low   close   volume  buy_volume\n", "0  2024-12-03 03:20:00  2.5081  2.5103  2.4991  2.5016  98409.5     33103.7\n", "1  2024-12-03 03:21:00  2.5014  2.5069  2.4992  2.5062  21917.6      9541.2\n", "2  2024-12-03 03:22:00  2.5056  2.5087  2.5000  2.5082  28555.5     12828.1\n", "3  2024-12-03 03:23:00  2.5082  2.5082  2.5010  2.5027  24897.9      5087.0\n", "4  2024-12-03 03:24:00  2.5028  2.5095  2.4976  2.5068  41689.5     24589.2"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["filepath = '../../sample_data/THE_USDT.csv'\n", "data = pd.read_csv(filepath)\n", "data.head()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x900 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set_style(\"darkgrid\")\n", "plt.figure(figsize = (15,9))\n", "plt.plot(data[['close']])\n", "plt.xticks(range(0,data.shape[0],2000),data['timestamp'].loc[::2000],rotation=45)\n", "plt.title(\"Stock Price\",fontsize=18, fontweight='bold')\n", "plt.xlabel('Date',fontsize=18)\n", "plt.ylabel('Close Price (USD)',fontsize=18)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 41560 entries, 0 to 41559\n", "Data columns (total 1 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   close   41560 non-null  float64\n", "dtypes: float64(1)\n", "memory usage: 324.8 KB\n"]}], "source": ["price = data[['close']]\n", "price.info()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_88601/1743761331.py:4: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  price['close'] = scaler.fit_transform(price['close'].values.reshape(-1,1))\n"]}], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "scaler = MinMaxScaler(feature_range=(-1, 1))\n", "price['close'] = scaler.fit_transform(price['close'].values.reshape(-1,1))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def split_data(stock, lookback):\n", "    data_raw = stock.to_numpy() # convert to numpy array\n", "    data = []\n", "    \n", "    # create all possible sequences of length seq_len\n", "    for index in range(len(data_raw) - lookback): \n", "        data.append(data_raw[index: index + lookback])\n", "    \n", "    data = np.array(data)\n", "    test_set_size = int(np.round(0.2*data.shape[0]))\n", "    train_set_size = data.shape[0] - (test_set_size)\n", "    \n", "    x_train = data[:train_set_size, :-1, :]\n", "    y_train = data[:train_set_size, -1, :]\n", "    \n", "    x_test = data[train_set_size:, :-1, :]\n", "    y_test = data[train_set_size:, -1, :]\n", "    \n", "    return [x_train, y_train, x_test, y_test]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x_train.shape =  (33232, 19, 1)\n", "y_train.shape =  (33232, 1)\n", "x_test.shape =  (8308, 19, 1)\n", "y_test.shape =  (8308, 1)\n"]}], "source": ["lookback = 20 # choose sequence length\n", "x_train, y_train, x_test, y_test = split_data(price, lookback)\n", "print('x_train.shape = ',x_train.shape)\n", "print('y_train.shape = ',y_train.shape)\n", "print('x_test.shape = ',x_test.shape)\n", "print('y_test.shape = ',y_test.shape)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "x_train = torch.from_numpy(x_train).type(torch.Tensor)\n", "x_test = torch.from_numpy(x_test).type(torch.Tensor)\n", "y_train_lstm = torch.from_numpy(y_train).type(torch.Tensor)\n", "y_test_lstm = torch.from_numpy(y_test).type(torch.Tensor)\n", "y_train_gru = torch.from_numpy(y_train).type(torch.Tensor)\n", "y_test_gru = torch.from_numpy(y_test).type(torch.Tensor)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["input_dim = 1\n", "hidden_dim = 32\n", "num_layers = 2\n", "output_dim = 1\n", "num_epochs = 100"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["class LSTM(nn.Module):\n", "    def __init__(self, input_dim, hidden_dim, num_layers, output_dim):\n", "        super(LST<PERSON>, self).__init__()\n", "        self.hidden_dim = hidden_dim\n", "        self.num_layers = num_layers\n", "        \n", "        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True)\n", "        self.fc = nn.Linear(hidden_dim, output_dim)\n", "\n", "    def forward(self, x):\n", "        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()\n", "        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).requires_grad_()\n", "        out, (hn, cn) = self.lstm(x, (h0.detach(), c0.detach()))\n", "        out = self.fc(out[:, -1, :]) \n", "        return out"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["model = LSTM(input_dim=input_dim, hidden_dim=hidden_dim, output_dim=output_dim, num_layers=num_layers)\n", "criterion = torch.nn.MSELoss(reduction='mean')\n", "optimiser = torch.optim.Adam(model.parameters(), lr=0.01)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch  0 MSE:  0.35791975259780884\n", "Epoch  1 MSE:  0.2980746030807495\n", "Epoch  2 MSE:  0.262790709733963\n", "Epoch  3 MSE:  0.23721303045749664\n", "Epoch  4 MSE:  0.20158252120018005\n", "Epoch  5 MSE:  0.13275571167469025\n", "Epoch  6 MSE:  0.06614705175161362\n", "Epoch  7 MSE:  0.03991030901670456\n", "Epoch  8 MSE:  0.06280304491519928\n", "Epoch  9 MSE:  0.10188798606395721\n", "Epoch  10 MSE:  0.057345371693372726\n", "Epoch  11 MSE:  0.03511641547083855\n", "Epoch  12 MSE:  0.013543227687478065\n", "Epoch  13 MSE:  0.005736880004405975\n", "Epoch  14 MSE:  0.009115728549659252\n", "Epoch  15 MSE:  0.010559392161667347\n", "Epoch  16 MSE:  0.008744941093027592\n", "Epoch  17 MSE:  0.009406359866261482\n", "Epoch  18 MSE:  0.015373129397630692\n", "Epoch  19 MSE:  0.01575222983956337\n", "Epoch  20 MSE:  0.013468235731124878\n", "Epoch  21 MSE:  0.014025923795998096\n", "Epoch  22 MSE:  0.01439647562801838\n", "Epoch  23 MSE:  0.012537064030766487\n", "Epoch  24 MSE:  0.009333491325378418\n", "Epoch  25 MSE:  0.007068275008350611\n", "Epoch  26 MSE:  0.006404064130038023\n", "Epoch  27 MSE:  0.005066283512860537\n", "Epoch  28 MSE:  0.0026175742968916893\n", "Epoch  29 MSE:  0.0009730992605909705\n", "Epoch  30 MSE:  0.0006777043454349041\n", "Epoch  31 MSE:  0.0010001130867749453\n", "Epoch  32 MSE:  0.0013323071179911494\n", "Epoch  33 MSE:  0.0015224297530949116\n", "Epoch  34 MSE:  0.0016796342097222805\n", "Epoch  35 MSE:  0.0019780828151851892\n", "Epoch  36 MSE:  0.0024270229041576385\n", "Epoch  37 MSE:  0.0027281029615551233\n", "Epoch  38 MSE:  0.002577058970928192\n", "Epoch  39 MSE:  0.002045361790806055\n", "Epoch  40 MSE:  0.0014482198748737574\n", "Epoch  41 MSE:  0.0010099186329171062\n", "Epoch  42 MSE:  0.0007594392518512905\n", "Epoch  43 MSE:  0.0005950527265667915\n", "Epoch  44 MSE:  0.0004106219275854528\n", "Epoch  45 MSE:  0.00021922297310084105\n", "Epoch  46 MSE:  0.00013900120393373072\n", "Epoch  47 MSE:  0.00023797625908628106\n", "Epoch  48 MSE:  0.000424267171183601\n", "Epoch  49 MSE:  0.0005361245130188763\n", "Epoch  50 MSE:  0.0005332700675353408\n", "Epoch  51 MSE:  0.0005138753913342953\n", "Epoch  52 MSE:  0.0005462794215418398\n", "Epoch  53 MSE:  0.0005842534592375159\n", "Epoch  54 MSE:  0.0005513272481039166\n", "Epoch  55 MSE:  0.00044259647256694734\n", "Epoch  56 MSE:  0.0003254029725212604\n", "Epoch  57 MSE:  0.00025832842220552266\n", "Epoch  58 MSE:  0.00022998328495305032\n", "Epoch  59 MSE:  0.00019219958630856127\n", "Epoch  60 MSE:  0.0001364448544336483\n", "Epoch  61 MSE:  0.00010109096911037341\n", "Epoch  62 MSE:  0.00011120812268927693\n", "Epoch  63 MSE:  0.00014724431093782187\n", "Epoch  64 MSE:  0.0001742206804919988\n", "Epoch  65 MSE:  0.0001802236947696656\n", "Epoch  66 MSE:  0.0001799063029466197\n", "Epoch  67 MSE:  0.00018805460422299802\n", "Epoch  68 MSE:  0.00019831262761726975\n", "Epoch  69 MSE:  0.0001928770070662722\n", "Epoch  70 MSE:  0.00016861430776771158\n", "Epoch  71 MSE:  0.00014181624283082783\n", "Epoch  72 MSE:  0.00012658089690376073\n", "Epoch  73 MSE:  0.00011999005801044405\n", "Epoch  74 MSE:  0.00011178283602930605\n", "Epoch  75 MSE:  0.00010063336230814457\n", "Epoch  76 MSE:  9.50573303271085e-05\n", "Epoch  77 MSE:  0.00010008813114836812\n", "Epoch  78 MSE:  0.00010934320016531274\n", "Epoch  79 MSE:  0.00011376522161299363\n", "Epoch  80 MSE:  0.00011328146501909941\n", "Epoch  81 MSE:  0.00011390028521418571\n", "Epoch  82 MSE:  0.00011682330659823492\n", "Epoch  83 MSE:  0.00011712346895365044\n", "Epoch  84 MSE:  0.00011211580567760393\n", "Epoch  85 MSE:  0.0001054221938829869\n", "Epoch  86 MSE:  0.00010148072033189237\n", "Epoch  87 MSE:  9.983916243072599e-05\n", "Epoch  88 MSE:  9.747201693244278e-05\n", "Epoch  89 MSE:  9.4308823463507e-05\n", "Epoch  90 MSE:  9.306021092925221e-05\n", "Epoch  91 MSE:  9.458253771299496e-05\n", "Epoch  92 MSE:  9.661885997047648e-05\n", "Epoch  93 MSE:  9.72368216025643e-05\n", "Epoch  94 MSE:  9.712145401863381e-05\n", "Epoch  95 MSE:  9.76418741629459e-05\n", "Epoch  96 MSE:  9.83548306976445e-05\n", "Epoch  97 MSE:  9.786520240595564e-05\n", "Epoch  98 MSE:  9.619486809242517e-05\n", "Epoch  99 MSE:  9.469946235185489e-05\n", "Training time: 61.60529828071594\n"]}], "source": ["import time\n", "\n", "hist = np.zeros(num_epochs)\n", "start_time = time.time()\n", "lstm = []\n", "\n", "for t in range(num_epochs):\n", "    y_train_pred = model(x_train)\n", "\n", "    loss = criterion(y_train_pred, y_train_lstm)\n", "    print(\"Epoch \", t, \"MSE: \", loss.item())\n", "    hist[t] = loss.item()\n", "\n", "    optimiser.zero_grad()\n", "    loss.backward()\n", "    optimiser.step()\n", "    \n", "training_time = time.time()-start_time\n", "print(\"Training time: {}\".format(training_time))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict = pd.DataFrame(scaler.inverse_transform(y_train_pred.detach().numpy()))\n", "original = pd.DataFrame(scaler.inverse_transform(y_train_lstm.detach().numpy()))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************************/YVpVHnpkjqvAYA8NsryzHSvMG+Ty1yjD1vExwcDIVC4TTpTkpKilM2pU1YWJgsq7Ko8gBQv359BAcH48KFCx4FLSUJFfJQV1HncScy3Pr+lJBogdEo4WL+pDyN6yo8bpdtBvFLiWboDVKV+tLGncq+P+Qe74134/3xXrw33q0y7g8n4iEiIso30PK3fdkkFP97PTWM+O6vvCLL/XfcgNGz07HnhKEkzbN75fMMSCbrw3vqHcOQggBcQB10HdULWh8Fguu4/+YyTy/h4BkTzGZg11Gj23LFkZFtwai30vHWl1mlqqe4RMn6mp8zOSY2+dWnl6zMC+Yv7ctXpiyqkHZ56ie/e/CR4mH8JlqDVHnXknHorBF5ev4v3Vuo1Wq0bt0a8fGOWeotFgvi4+PRoUMHl8e0b98eO3bskG3bvn072rdv7/Y8165dQ3p6OsLDw8uk3dVN/VqOQGNCkhlmM+CnFRAe5PkjTFigdYxMswX24CcRERF5NwYtiYiIXFCFup8YY7/QQrZ+p2Un/tuViItFzEr73jfZyDMA737tPCZjcR08Y8SZSwZoYA18+gbpYH5tAXxnvGsfrFNSu5/UJSfPjPrSVfQx7wBQuiDZzqNGSBJw6KypVPUUxWSW0MPyHzYZn8K7xg8RAUc2m/LB/8Ml1HJ5nCos1OX2yvbcyFD8V7sXjghNAABiegreXJGFud9VTPCXimf06NFYvXo11q5dizNnzuD1119Hbm4uBg8eDACYOnUq5s6day8/atQobNu2DcuWLcOZM2cwf/58HD58GCNHWoeTyM7OxjvvvIP9+/cjISEB8fHxGD9+PBo2bIju3btXymv0dpH5M4gnpVtw/KL1faZhHUWJMrsFQbBnW567wqAlERFRVcCgJRER1TiBuqI//vwCfd3uy+73CA4JTbEv/Hb7th+ML+DNhZfdHiNJEhSSCTGWE1BJJc9w3H88F5uMT6GRdBUAoBdUEHx8AK0jUKlRu399qpRrWGGcgZfNy1D/8u4StwMAcgtkBubklV+WYJ5BwgzTZwCAztIx+/Zz4R3QPkqDg48thBnyIIYF3tv1s0V9JT6cFIgkIQQA0Ei6jHGm75F9yvVEPVQ57rnnHkybNg3z5s3D/fffj2PHjmHJkiX27t5Xr15FUpJjaquOHTvi/fffx3fffYf7778fv/32GxYuXIgWLaxfcigUCpw8eRJPPfUU+vbti+nTp6N169ZYtWoV1AXG0CWHAD8RgX7Wv+V/Dlq/qLEFHkuiSYQ1g942CzkRERF5N45pSURENU6wv4jnH/KDVi0PbF1HMGojrcjjW3SPBrrPwR+f/okO2GrfPgbrAUxwecyGeD2+MM5APSThgNAcwNslavuZHadk6wbJ+aNcoxbwn9DSHuD7WPEQnjF/AwCwGBxd08NSTwMoeYZX8g0LnjZ9DR/ocfHaJEQ3UpW4rsJYLK63ZzftAH8AvW7xQ9YXvgiEI4P1+OBX4e0dbjO1IUAWUBcpGG75HcMtv+NC3mr4ar034FrTjBw50p4pebOVK1c6bevXrx/69evnsrxWq8XSpUvLtH01QWQtBW6cM+FUgmM8y5KyHXuWmZZERERVAjMtiYioRrolWo22TeRBth1iDADAUMzv9FJz5Q/PjcPcPwiv3ZaHerBmZbWTTrktV5RAyLsQRzYKclnuuNDYvhwzZgAS8rtQHznumMTGYilddmSg2oiBlr9xtyUeu7ZeKFVdhTG5uaw3TBoA1lnQCwYsL0z5DOG3tC239pSVDl3qOG3jpDxEcrZxLW0a1S15zkWTCGtdFxPNMJo4hiwREZG3Y9CSiIgo35c+Q/CZYjD+T/V6scqnp+pl65LG/ViSvtnyWYUTrpesi3hvyy7ZuiLU9biNPV96EHsDuuB8v4lo01gFU/5s6Dv2OIKW566UbiKeVhGOLpYFMzhLIydPwsYdeUjPdKRXmkyuUy199ekAAMVNs7H7hgaVSVvKW6d2QU7blEpmWRIVZBvXEgDUKiAitOSPL+FBIvx8BJjN1sl9iIiIyLsxaElERJQvR9DiW0VfXBFcT+xys5CG8g7IguSmHzOAIeZNsvUZC6/j8XfScTXFswfnupIj+Hkw5FZA6bpLtlrng/ovvQC/23sAcMyGPts0317GkFe6cd1EgyNoe+FCTqnqslm0LhtfbMzFWysdGaUmvevgap1+vVxuRwkm6agM9WrJM8a21rkXEEve9ZWoOiqYadmgtgIKRcn/vgVBQBNOxkNERFRleF3Qcvfu3XjyyScRFxeHqKgobNq0qdDyv//+O0aPHo1bb70VHTt2xPDhw7Ft2zZZmfnz5yMqKkr207dv3/J8GUREVAXVC/MsYJQbGWWfARoARJP7zEUT5HV/aJqLzGwLnp2X4eYIOUmydmVMEqyzms9XPIi6L0wpdltzzM7BzTApvdjHu2LIzrMv15MSS1WXzc6j1mt48boZu48bkJBoxqufpjiVS7xtCJTBQfb1rSG9y+T8FW2H0Ma+3OL/BldiS4i8U2Qtx+NK4zqlD+rbx7XkZDxERERez+uCljk5OYiKisKMGTOKVX737t2IjY3FZ599hh9//BFdu3bFU089haNHj8rKNW/eHP/884/95+uvvy6P5hMRURX23DA/dGurxuxx/sUq37CuEpNUL2KR4gEAgGB2/xAc3raJbL2plIC/jONwpzm+yPPkGSQ8+Ho6hs9IQ6BkzUAU/IvXRhtX43TGSfs9quNm3//mmLToJfNy/LlHj+Ez0vDlr4VnXVosEobPSMPwGWlIy7wpO1WScKc5HsFSBt7/JhtTFmagk+WYUx1iL/lkJ+r7H8ROoTV+bDqu5C+oEuxsPhQAkAFfKHS6Sm4NkffR+YgI9rdmV5ZmPEsb2wzinIyHiIjI+3nd7OE9evRAjx49il1++vTpsvXJkyfjzz//xF9//YVWrVrZtysUCoSHe/s8okREVJlqBSvw9AN+xS7fPUaNrFwJvvFqIAkQLK6DlueumrDvaC5c5QK+ZF6OJNxT6Hk+XpONx00/og5SEIhMAECHDq7HsnTHD7lO284IkYjfmYe7u2o9qssmSpJPvvP5uiz0tuzGtu2tMaqvr9vjTlwyIVDKRFPpEp58ryW+mxUCADCbJdxj+QfPm1cCZuD/lK8jT1Bjhvkzpzokrbz+ZlFBSH/5NTTyrRpdw236D43GrJ8/QdwtAWhbRbq1E1W0OzposP2wAZ2iXA+H4YnGtsl4rpthMkkcR5aIiMiLeV3QsrQsFguys7MRFBQk237hwgXExcVBo9Ggffv2mDJlCiIiIjyuv7yfJ2z187ml+HjNPMdr5jleM8/VhGumUAi4N1aLAyc1QBIgmo0uX++LizLxf+Ykt/XcfK1uruPyiWt43/KrbFumwt+ja9tSOu+0ramUgE9+3ou+t8YWv6ICbrEcka1PNy9BL8t/gBlIFta4PW7t5iysNVq7tn8l9oMg/B8AIPmGxRqwzLfM9DpyoHFZh6CyBi8KXrMgf6/rQFKkIH8R4x+quC9Vq/PfI1VfD/b2wYO93U905onawSL8tAKy8yRcSjKjcRlkbxIREVH5qHaf0kuXLkVOTg769XN0G4uJicHs2bPRuHFjJCUlYeHChRgxYgTWr18PnYddsUJDPeuOV1IVdZ7qhNfMc7xmnuM181xNuGZqH2tgTQUzwsKcX+8k0ycYZNns9nilxhdB/o6x2m6+ZhE+OcBNw2XWaVzL5bk8Ndf0IRB2d4mO3SDUR6x00L7ey/KffbmwtpnOOMr1t/yDoNCnYbYAw1+9gD9vKusL+QztbwVPQavWIRh0U/014feMiEpPEAQ0rqvA4XMmnLvCoCUREZE3q1af0uvXr8fChQvxySefIDTU0W2uYHfz6OhotGvXDj179sTGjRsxdOhQj86RkpKJ/LkQyoUgWB+8yvs81Qmvmed4zTzHa+a5mnTNsg3W9DV9jgHJyZmyfRaLJAtYXkcIaiNVVmbz9kR076Rze816tbYANw19GdnI3+lchQkrZJ8n9QDW1zTyjXRMQJbbMteupEOpdj1pRh/LDvvy32JnJK9NQscWKvgiz2X5gsZNu1XW5pr0e1YWbNeLqCazBS3PXjWjV2U3hoiIiNyqNkHLX375Ba+88go+/vhjxMYW3s0tICAAjRo1wsWLFz0+jyShQh6KKuo81Qmvmed4zTzHa+a5mnDN4k9I6AvAkGdweq258kRB6B99DljxqmxbgJQFSXJk/t98zTRm52CeJCqAEl7XJYqBeNz8k33dbDRDVBZvVt59p4zYvFeP2w27cL9li9tyk964iA9eaQSt2rk/8t2W7fblQZbNmPZ7W3RpGQsdCp/AB3D/u1QTfs+IqGw0jlAC0OPcFc4gTkRE5M2q3uBPLvz888946aWXMHfuXNxxxx1Fls/OzsalS5c4MQ8REZUJ28zcQZIjY/G/4wYs+yUH/xw0yMpqAv2xva584p1AqfBMx7TkooN5ngh64AGMUb5mX9+9JwUnLhb98J6YZsacr7Jw8kgKXjEvKbRsG8tprNvmOnNyV7h8SqJ3TPNw4ZoZ3xhfLrTOk0KDIttIRFSUJvmT8Vy4bobJzG87iIiIvJXXBS2zs7Nx7NgxHDt2DACQkJCAY8eO4cqVKwCAuXPnYurUqfby69evx7Rp0zBt2jS0a9cOSUlJSEpKQmam4wHwnXfewa5du5CQkIC9e/di4sSJEEUR9957b8W+OCIiqpaa17dOClMfiZDMZgDAe99k46+dWVjxc4asrKjTwXjHANk2RU7hQctLFzIK3V8cM5TjYIASb6qeQI/2Gkx9obV93/71e7FoyRmYTIU/vH/xSzb+MozFD8bniz6f+TNs//eafT0j22JfVqicszr3H8uWrW8UnXtNpI2cVuR5iYiKUjtYhK9WgNEEJCSaK7s5RERE5IbXBS0PHz6MgQMHYuDAgQCA2bNnY+DAgZg3bx4AICkpCVevXrWXX716NUwmE2bNmoW4uDj7z1tvvWUvc+3aNUyePBl9+/bFs88+i6CgIKxevRohISEV+tqIiKh6stSp71jRW7MLnzStwW/GCfja+JK8sK8Oh6/Ig3bK3MKDlgMsW2Xr6fBsEjkA2CZ2Qn/VPGxT3QIACAkQcUaIBAC8Yl6Kr4yv4OwFa+BwzwkDnnw/HVk5FlkdaSfOu6z7aEB7l9un5y3Clv16vL0yE0+8ewNrt+YCsM6yfrNWN/bZl48ITXAmrKNs/9diXzRpVavoF0pEVARRFNCkrvV9+MwVBi2JiIi8ldeNadm1a1ecOHHC7f45c+bI1leuXFlknR9++GGp20VERORO6w517BPlSAYDBF8/DLP8AQAIww17uVNCfQQpFOjcVgcUGA5SMMm7kNts3qvHobMmjCqw7Tzq4ss6j+MpD9s45UE/LP05B08/4Gfflgb5hCx/Lt+OJjPvRPxXWzHdvAVT5/wfPpnVGADw9R85aC5dcln3JaEOWrnY3lo6i/Xfb0IUstBBysWmTbfinlujIJicg5ZarSOQe6Dfi7i3cyAwa4F9W2fpqCcvl4ioUE3rWSfjOXPZhN6dNJXdHCIiInLB64KWREREVU1EuBK50MAHesCgR57edTfrwLfmWsuHKZEOHYLyZ98WzK7Hk1y0zjqW5d0IRXNcwkeKh/E/xR24p4XnD9hdWqpxS7QKguCYGEcPtazMa+bP0XdmO/xqWgwAGGv+AYC1K/iBrWexzPyFU72nhPq4lO1rX7dAgFhghqAXCxxzv+VvPPz+h5iUbZ2dKBmB9qCu4uRB63mE5ugVGwRRBKY2eA/vXnzB49dKRFSUJvmT8ZxlpiUREZHX8rru4URERFWNIAB5tgCgQQ+Di7EhlyvuswcMtRoBI1Vv2SeWcZV5CAADzX9hvnEO6kvWsSG7dA3DyLt8MLy3TwnbKZ/J+7Ig726dggAMtPxtX4+1HLAvv2xe6lRfLjRIn/gOAkXHREGLbpuHX8Q4l+cPRDb65/yBvvmzh5+I6m/f189o7QKvDtBBoRAgCAJeeKoRzLC2+VS30cV5iURExWKbjOdiohkGIyfjISIi8kYMWhIREZWSIDiyFm+k5cFkcM6c1DRtIlsfdk8Izgr1rMe7GOMRAJ42f4vW0lk0hDVo2bBZKAZ000KrFlyW91QdKVm2HooMPGn+3r5+QtUUP/ydixtZlpsPBQDsC7gFjSJU2BVyOwAgCUEY1L82xJHjYITzZDsAMN68xr7sG6B12p+r8JWtv1p3Fp5XPos2vVs7lSUiKqnwIBH+vgLMZuDidWZbEhEReSN2DyciIiolsUCm5dlzOVj50xWsuqlMx5hA2Xq/W7U4vlkNZLjvHn4zdUhQGbTWIUfwAQpJMGppPI2A31/Goj8fwPtSgn37KaE+tgvtcKb+PWgOoFn7SNz55ycQRBGrFAIiwlVIRhDqIqXQ82foFbiKUFm50NQzsjLPTIyC0QRoyihQS0QEWDPPm0YosP+0CWeumNAsko9FRERE3oafzkRERKUkCIBeUAMSIJr00BiyncqofJ3HoTQL1o/h4gYthcCgwmKMHtvQ+FH0PbPd7X4f6NFMSsD7po9k298OfxE50OC1/tZZzO/rpkVooIi2TVQAgIgwBa4IIagrFR60VCUlOAU2G+Iakgqsi6IAjXzoTSKiMtEkQon9p00c15KIiMhLsXs4ERFRKRXsHp6SnIuA/Al2LiPcXsbsosu4QcrvQu1iTEuz2Tk8KWlKNpalO1NHBSMZgUUXLODNBrPw9tPhWPBcIGoFW9uvVAro0V6DkADHfytOCg2LrCux3d2Ypnxatu21Wq961B4iopJqUs/6HnbmcvG+OCIiIqKKxaAlERFRKRXsHq66cAJNdRkAgDxtIDaJXXBCaIjs2k2cjkvKsj4wX7mmd9q3/7SLcS6Fsu0irVELmNP8bTwe9A5SEVCsYx4dVhdKRdHtSFKGy9bXij2dynTrUQ9+nTric8UgAMD/KWdg6KMcu5KIKkbTCGu2e0KSBXkGTsZDRETkbdg9nIiIqJQKZlo+YPkTSP8TAGDx1eFty+MAgBXBzn2c9YJ1m05hcNpnSE2XrccLMWhWlo3ON3V0OEzmcChfdWQa7RNaoIN00mV5daB/sepNsjjKTVE+BzNEDLJsdio3pr8vHtnXD98o+uGdp/xRJ8T1BD5ERGUtJEBEsL+AtEwJ56+ZEd2Aj0ZERETehJmWREREpSQIAvIE56Dk9TTg86mB+HRKILQa5+zExk2sY0IGiHlO+4LFLPvyTOVYLK41rgxb7CAIAlRKAVcFR2bkq9pncBWhLsuLyuIFFdu0cnQ7v+eBKNzVq5Zs/3WEAADUKgGfTA7Ee+MD0KgOAwZEVLGa5Gdbsos4ERGR92HQkoiIqAx0tBxz2tZVOoQAP1E21mNB59Ksk/OkJztP3HPluqN7eOvB3THzyXCnMmVpjnI0dgutMEk5FZ9MC8cxsXGp6jud6gjitmkdhKN5dWT7J6mm2ZdDA0U0qM0MSyKqeE0jrO89nIyHiIjI+zBoSUREVAZCkeG07bjQqNBjzqdbg5Y65DrtO3fOmmmZgFro1lYDX23Zjmd5s+53N8U01bPoOqAtfDQCvhPvdirzhuLxYtd31NIQf4m3YIV4L0SlAg/18ZFNTNT99rpl0m4iotJoUs+aaXn2CjMtiYiIvA2DlkREROVkQ4NHCt2v81cBAFpLZ532dWpgfYDOFTQQK+DTekA3LT57IRB3d9ECALJrNUYu5F3eG3WNKnZ9o+/V4U3lE1ihvA+AtRv4hz6jkQs15ikexJA7tGXXeCKiErJlWl5JtiAnj5PxEBEReRMOHkVERFROarctvIt1s8BcIM26LJlMEFSOj2XzyaMAgEgpEVli+WZZ2gTqHNHRKQ/qMGr+mwiXUvGmaSEAAY1iIopdV+vGKrz5hD9qBTnqPKFqjgGmj2ERFHhIWTGviYioMAF+IsKDRCSlW3D2qgltGqsqu0lERESUj5mWREREZeD/lK/L1n8R43B3V59CjzlR6xb78thZV2T7+qZvAAD4QF82DfRQZLgCDw6JQGCbKIxSvYkRqrcQ3dB5sqHCNI9UygKhk4f7QaFSYOwA37JuLhFRiTWxjWt5meNaEhEReRMGLYmIiMrAeTECvdSf2dcFSFAWkU1Yt7YjqPmTcUq5ta2kbm+nwXPD/NCuTQD6xAaUur42TVRY8XIQenfWlEHriIjKhn0GcY5rSURE5FUYtCQiIqokPdrflLlosQAAMhOu2zfNVwyvyCY5EQQBzw7TYVTfssmOVCjYLZyIvEvTepxBnIiIyBsxaElERFQOlHWLHv/RKRPTaO0KXnuBI+syQahdpu0iIiK5xnWtQcvENAsycyyV3BoiIiKyYdCSiIioDLz6mA6hAQKeVz6L78XeuBxzT5HHKETggNDcvi4Y9Nj4RTx8C4xj+Z/QqlzaS0REVjofEXVCrI9FZ5htSURE5DU4ezgREVEZaNNYhU+mBGH4jFbYK7bCw2LRH7EqpYCpymfxm3ECAEDKy0O/f2bKykwa6l8u7SUiIoem9RS4lmrB2csmtG9W8TOI6w0Sjp434Ua2BTeyJWTk/wsJ6N1ZjZYNOas5ERHVPAxaEhERVSKj4HgQvbD6F4TdtL9bW89m7CYiIs81iVDi30PGSsm0zMmTMP3zDFxJdt01fdtBA9o1U2J4Lx80rVe8xzeLRUJWrgQ/rcCxhImIqMpi0JKIiKgchAR4/pDY6dKvsvWfxDvQrawaREREbjWJsE3GU7EziEuShM/XZ+NKsgX+vgKaRCgQ4Cci0E9AgJ+I66lm/L3PgAOnTThwOhO3RKvwQE8tcsxGnDxrRGKaBUnpZiSmW3Ajy5qhmZEjITNHgiQBAX4CenfS4M7OGoQGcmQwIiKqWhi0JCIiKkOTh/vh+AUTurUpXobkp1MCgdmu981XPMigJRFRBWhSVwlBAFIzJFxPNaN2iKJCzvvXXgO2HzZCFIGpD+vQor7z49n9cVp8/3ceth00YPdxI3YfNwLILFb9GdkS1m7Nw7p/8nBLtAp3d9GgVSMlBIHZl0RE5P0YtCQiIipDXVup0bVV8bt0hwSIMEOEAvJugQNUH0ESmBVDRFQRtBoBrRspcficCWs252HiEL9yP+elRDO+2JgDAHiwl4/LgCUA1A5RYMJgP9zfXYs1m3Ox86gRWrWA8CDR/hMWKCIkQESAn4BAPxEBvgJ8tQL2nTLi1516HD1vws6jRuw8akSTCAUmDPJDZK2KCcwSERGVFIOWRERElexl5US8Y5pnX48XYpAt+KJ+LQYtiYgqysN3+uDlzzKx7aABfW/VoFkxx48sCb1Bwkers2AwAu2aKTGgm6bIYyLDFXhumA5mi4TatfyRkpIFSSr8GNsXaRevm/H7rjxsPWjA2StmvPRZBh7r64tendTMuiQiIq/FpyEiIqJKlgGdbH27GAMAeP4hnaviRERUDprWU+L2dtZM+S9/zYFUVEQwn8kkYd8pI77+IxfbDxtgMhV93Bcbc5CQZEGwv4AJg/wgisUPHCoVgseBxga1FXh8gB/mPR2ImKZKGIzAZ+tz8OHqbGTlup4AiIiIqLIx05KIiKiSnRAaytaz4YO3nvBHnQoaU42IiKwe7O2DHUcNOHHRjJ1Hjbi1tevhPgxGCQfPGLHjqBF7ThiRk+cIVAbqBPTqqEGfThqEBclzRPIMEv49aMBfew0QBGDiYD8E6ioujyTIX8RLI3X4JV6PbzZZu5qfuZyJSQ/4IboBHw2JiMi78JOJiIiosgkClivuw2jz/wAA2YIPmkXyI5qIqKKFBoq4r5t14ptVf+SiYwsV1CpHVqMkSdiwQ481m3ORq3ccF6QT0KaJCkfOGZGWaZ385qdteejYQoUQfwFXUyy4kmJGaoYjuDn4di3aNFFV5MsDAIiigAHdtGjZSIl532fjeqoFry/LRLe2ajxwhxZ1Q/mFGREReQc+EREREXmBTPjal3OgrcSWEBHVbAO6afHnHj0S0yz4dace98VZ35Nz8iQsWpeNnUeNAIDQAAFdWqlxays1WtRXQBQFmMwS/jtuxB+79Th8zoQ9J4xO9fv7CujSUoUhPSr3vb5ZPSXeeTIAyzfkYMt+A/45aMD2wwb0aKfG4B5a1Apm8JKIiCoXg5ZEREReIAOOmWpH9A+qvIYQEdVwWrWAh/r44JO1Ofhxay56tFcjPcuCD1dn42qKBQoF8MhdPri7i8ZpLEqlQsCtrdW4tbUal5PM2HbQAEhA3TARdUMViAgVofP1nmkFfDQCxg/yQ79bNVj9Vx72njRi8z4Dth404I72arRspEREqAJ1QxXw1XLCHiIiqlgMWhIREVWyiUN8sWuNI9MyIJiZlkRElal7jBq/7tTj7BUz5n6XhbNXzDCagNBAAc8N06F5MYbwqBeuwIO9fSqgtaXXuK4S00bocPKSCWs25+LgGRP+3GPAn3sM9jJBOgERYQq0aqREpygVGtdVcOZxIiIqVwxaEhERVbLuMRqc+j7Lvi6q+PFMRFSZRFHAI3f7YObyLJy4aAYAtG+mxMQhfvD3okzJstaivhLTR/nj6Hkj/jlowJVkC66mmJGeJeX/mHD0vAnf/52H0AABHVuo0ClajbaNlVAqGcAkIqKyxaciIiIiL7AVHTANywEAki6gkltDREStGqnQs4MaWw4Y8EAPLQbdrnXqDl5dtWqkQqtGjkmCcvIkXE0x48I1M/aeMuLgaSNSMiT88Z8Bf/xnQHiQiIfv9MFtrVXMviQiojLDoCUREZEXyBW0eEw1EwrJjGk+VaM7IRFRdTfufl+MvscXGnXNDsT5agU0radE03pK9OqkgcEo4ch5E/YcN2DXMSOS0i34eE02folX4JG7fBHdkI+ZRERUevw0ISIi8hIXhbqAACg5YSsRkVcQBAEadWW3wvuoVQI6NFehQ3MVRt4t4ZfteVj3bx5OJ5gxY1kmurRUYXhvH0SGe+8HmsUiITtPQlauhOzc/H/zJOTkSVAqrK9RrQRUSgFqFRCkExEWKEKtqtkBbCKiisSgJRERkZdRVN/h0oiqlFWrVmHp0qVISkpCdHQ0Xn31VcTExLgtv3HjRnz88ce4fPkyGjVqhOeffx49evSw75ckCfPmzcOaNWuQkZGBjh074vXXX0ejRo0q4NUQlQ+tWsCQO3zQu5MGqzfn4q+91uzLXceMaFFfgR7tNbittQp+PmX/4SZJEkxmwGCUYDA5/s3V5wci8yzIyrEGIzOyJaRlWvJ/JKRlWWA2e37OQJ2A8EARYUEiagWJqBWsQO0QEXVCrEFNIiIqOwxaEhEReRmlglkcRJVtw4YNmD17NmbOnIl27dphxYoVGDNmDH799VeEhoY6ld+7dy+mTJmCyZMno2fPnli/fj0mTJiAH3/8ES1atAAAfP7551i5ciXmzJmDyMhIfPzxxxgzZgw2bNgAjUZT0S+RqEwF+YsYe58f+t2qxbd/5mLPCSNOXjLj5KUcfLERuCVahc7Ravj7CtD5WH/8tAJUSgFZ+ZmOWbkWZOZIyMqRkJkrITMnfz1/X64e0Bsl6A0S9EYJeQZAkkrXbh8N4KcVre3xEeCjEWA2SzCaAINJgsFoPWdqhgV6I3AjS8KNLDNOX3aOeCpEIDw4EwG+1szMYH8Rwf4CAv1E+Gqtddv/1QjQqKwZnUoFOBYoEZELgiSV9m2+ZklOziz1B2NhBAEIC/Mv9/NUJ7xmnuM18xyvmed4zTwz/fMMnE6wPgB9MyOoxkz2UFr8PfOM7XpR0YYOHYq2bdvitddeAwBYLBb06NEDjzzyCMaOHetU/tlnn0Vubi4WL15s3zZs2DBER0dj1qxZkCQJ3bt3x+jRozFmzBgAQGZmJmJjYzFnzhz079+/2G3j/0drtqpyf9IyLfjnoAF/79cjIdFS7ucTBECtAtRKa1DQTyvYA5F+WgEBvtYAYnCALZgoIshPKPas55JkDZ4mpVuQfMOCpHQLEtOsP9dSzUhKt8BoKnnbbQFMjcoazNSonZe1ase6SilAIVoDpUqFAFEEBAAmM2C2WDNQTfnBV71Rgt4Ie7BXb5RgMlnLGvPLmMyuf5kEwVov8v+1BVclSYIkARbJGji2/VgkwJK/T5IAUbAeIwi2ZUChAFQKa7BWaftXaV1WKQCl0vqvQuF4jaIIKETBXof9B45l0XYeEfbziaJ1u0K0njfAX4vs7Dx7m+yxYsnxj+3vquDrsEiAZMl/nbBtk2THFf77436bfVdhldz0Wm2vF7C+TgiOf23lnO5hgbqs/wj2/bLdBcsLsn+cyhZ8XQVfh+Rif2EEAfD31yIzM69c3teEgq/DzZ98cf/nXd2/X9D5CGjZUCl7Finrzx1P/j/KTEsiIiIv0DRCaQ9aKhSCVz+IElV3BoMBR44cwbhx4+zbRFFEbGws9u3b5/KY/fv347HHHpNti4uLw6ZNmwAACQkJSEpKQmxsrH2/v78/2rVrh3379nkUtCzvByb7w101fzCrqqrK/QkJEHFfnBYDumlw7qoZf+8z4PxVM7LzLPbxIw1Ga1lBsD4o+/sK8PcR4OcjIsBPcGzztWZC+mhswTt5UK8ishUFQUCAn4AAPxFN6znvt1gkpGVJMEpaXLic5eiGnmlBRrZ1rMxcvYSc/J9cvQRLfixXkoA8A5BnkFB0+Ksq8qbXlFPZDSC3siu7AZRv8nA/3NraMaBzWX/ueFIPg5ZEREReYHgvLbJyLRhwexCAEqZqEFGZSEtLg9lsduoGHhoairNnz7o8Jjk5GWFhYU7lk5OTAQBJSUn2be7KFFdoaMVky1bUeahkqtL9CQ8HurgYDtY6BqUEX41QLXoY1Kpl/bdN06KHe7CNx2nr7p5nsFgzIQ0ScvP/zdNbkGewBjjzDNblPL2EXL01q9OWUWk2S/ZMSaXCmoWpVCD/XwFajTVLU6sRrdmaagFqpbWc9cd6nFMgoUBWoZS/bpEkp8xJWzalLcNRITrqsmYqSvaMTIvFlgFqbbvRJN30Y+2WbzRJMJslmC2w/uQvWyyStU0WecajZEGBc0gw55/LYrEGh835/0qwbre1yWK5KQsvf+Hm12T79RRF+WsHHMHyon6DZdfXTQZjwSIFQ72W/Hi2I9PT8Xpgz3iVUCD5U3b/7Jtuih/bOt7KMiNvygB1ygi1rbhosyzD02mhZEEvTw9xmeVZREasy0xYT0940ya37famtzoXbff3E9GlbSDCQpzDhZXxucOgJRERkRfw8xHxzFAdwsJ8kJycWdnNISIvlpJS/t3DQ0P9y/08VDLV8f7kVZMEq9LcG40AaDQA3MY7y3KSH2/P6Mzv51zWtVbDv53qgvfGy1hyUfD71LK+P7b6ioNBSyIiIiKiAoKDg6FQKJCSkiLbnpKS4pRNaRMWFuaUMVmwfHh4uH1bLVs6Vv56dHS0R+2zjbFW3irqPFQyvD/ei/fGu/H+eC/eG+9WGfenLL+uISIiIiKq8tRqNVq3bo34+Hj7NovFgvj4eHTo0MHlMe3bt8eOHTtk27Zv34727dsDACIjIxEeHi6rMysrCwcOHHBbJxEREVFNxqAlEREREdFNRo8ejdWrV2Pt2rU4c+YMXn/9deTm5mLw4MEAgKlTp2Lu3Ln28qNGjcK2bduwbNkynDlzBvPnz8fhw4cxcuRIANYxz0aNGoVPP/0Uf/75J06cOIGpU6eiVq1a6NOnT6W8RiIiIiJvxu7hREREREQ3ueeee5Camop58+YhKSkJLVu2xJIlS+zdva9evQpRdHz/37FjR7z//vv46KOP8MEHH6BRo0ZYuHAhWrRoYS/zxBNPIDc3F6+99hoyMjLQqVMnLFmyBBpN0ZN2EBEREdU0giRxxABPJCeX/8DnYWH+5X6e6oTXzHO8Zp7jNfMcr5nneM08x2vmGdv1oqqN/x+t2Xh/vBfvjXfj/fFevDferazvjyf/H2X3cCIiIiIiIiIiIvIqDFoSERERERERERGRV2HQkoiIiIiIiIiIiLwKg5ZERERERERERETkVRi0JCIiIiIiIiIiIq/idUHL3bt348knn0RcXByioqKwadOmIo/ZuXMnBg0ahDZt2uDOO+/Ejz/+6FRm1apV6NWrF9q2bYuhQ4fi4MGD5dF8IiIiIiIiIiIiKiWvC1rm5OQgKioKM2bMKFb5S5cuYdy4cejatSvWrVuHRx99FK+88gq2bdtmL7NhwwbMnj0bEyZMwNq1axEdHY0xY8YgJSWlvF4GERERERERERERlZCyshtwsx49eqBHjx7FLv/tt98iMjISL774IgCgadOm2LNnD7744gt0794dALB8+XIMGzYMQ4YMAQDMnDkTf//9N3744QeMHTu27F8EERERERERERERlZjXBS09tX//ftx2222ybXFxcXj77bcBAAaDAUeOHMG4cePs+0VRRGxsLPbt2+fx+QShdO0tbv3lfZ7qhNfMc7xmnuM18xyvmed4zTzHa+YZXiciIiIiqiqqfNAyOTkZYWFhsm1hYWHIyspCXl4ebty4AbPZjNDQUFmZ0NBQnD171uPzhYb6l6q93nae6oTXzHO8Zp7jNfMcr5nneM08x2tGRERERFS9VPmgZUVLScmEJJVf/YJgffAq7/NUJ7xmnuM18xyvmed4zTzHa+Y5XjPP2K4XEREREZG3q/JBy7CwMCQnJ8u2JScnQ6fTQavVQhRFKBQKp0l3UlJSnDI0i0OSUCEPRRV1nuqE18xzvGae4zXzHK+Z53jNPMdrRkRERERUvXjd7OGeat++PXbs2CHbtn37drRv3x4AoFar0bp1a8THx9v3WywWxMfHo0OHDhXZVCIiIiIiIiIiIioGr8u0zM7OxsWLF+3rCQkJOHbsGAIDAxEREYG5c+fi+vXrePfddwEADz74IFatWoV3330XQ4YMwY4dO7Bx40YsXrzYXsfo0aMxbdo0tGnTBjExMVixYgVyc3MxePBgj9vHiXi8D6+Z53jNPMdr5jleM8/xmnmO18wzvE7VA/8/WrPx/ngv3hvvxvvjvXhvvFtZ3x9P6hEkybs6U+3cuROjRo1y2j5o0CDMmTMHL774Ii5fvoyVK1fKjpk9ezZOnz6NOnXqYPz48U4Bya+++gpLly5FUlISWrZsiVdeeQXt2rUr99dDREREREREREREnvG6oCURERERERERERHVbFV+TEsiIiIiIiIiIiKqXhi0JCIiIiIiIiIiIq/CoCURERERERERERF5FQYtiYiIiIiIiIiIyKswaElERERERERERERehUFLIiIiIiIiIiIi8ioMWhIREREREREREZFXYdCSiIiIiIiIiIiIvAqDlkRERERERERERORVGLQkIiIiIiK7VatWoVevXmjbti2GDh2KgwcPVnaTapzFixdjyJAh6NChA2677TaMHz8eZ8+elZXR6/WYOXMmunbtig4dOmDSpElITk6upBbXXJ999hmioqLw1ltv2bfx3lSu69ev4/nnn0fXrl0RExODAQMG4NChQ/b9kiTh448/RlxcHGJiYvDYY4/h/PnzldfgGsJsNuOjjz5Cr169EBMTgz59+mDhwoWQJMlehvem4uzevRtPPvkk4uLiEBUVhU2bNsn2F+depKenY8qUKejYsSM6d+6Ml19+GdnZ2WXaTgYtiYiIiIgIALBhwwbMnj0bEyZMwNq1axEdHY0xY8YgJSWlsptWo+zatQsjRozA6tWrsXz5cphMJowZMwY5OTn2Mm+//TY2b96Mjz76CCtXrkRiYiImTpxYia2ueQ4ePIhvv/0WUVFRsu28N5Xnxo0beOihh6BSqfD555/jl19+wbRp0xAYGGgv8/nnn2PlypV4/fXXsXr1avj4+GDMmDHQ6/WV2PLq7/PPP8c333yD1157DRs2bMDzzz+PJUuWYOXKlbIyvDcVIycnB1FRUZgxY4bL/cW5F88//zxOnz6N5cuXY9GiRfjvv//w2muvlW1DJSIiIiIiIkmSHnjgAWnmzJn2dbPZLMXFxUmLFy+uxFZRSkqK1KJFC2nXrl2SJElSRkaG1Lp1a2njxo32MqdPn5ZatGgh7du3r5JaWbNkZWVJd911l/Tvv/9KI0eOlN58801JknhvKtt7770nPfTQQ273WywWqVu3btKSJUvs2zIyMqQ2bdpIP//8c0U0scYaO3as9NJLL8m2TZw4UZoyZYokSbw3lalFixbSH3/8YV8vzr2wva8dPHjQXmbLli1SVFSUdO3atTJrGzMtiYiIiIgIBoMBR44cQWxsrH2bKIqIjY3Fvn37KrFllJmZCQD2bLHDhw/DaDTK7lXTpk0RERGB/fv3V0YTa5xZs2ahR48esnsA8N5Utr/++gtt2rTB008/jdtuuw0DBw7E6tWr7fsTEhKQlJQkuz/+/v5o164d3+fKWYcOHbBjxw6cO3cOAHD8+HHs2bMHt99+OwDeG29SnHuxb98+BAQEoG3btvYysbGxEEWxTIeVUZZZTUREREREVGWlpaXBbDYjNDRUtj00NNRpPEWqOBaLBW+//TY6duyIFi1aAACSk5OhUqkQEBAgKxsaGoqkpKTKaGaN8ssvv+Do0aP4/vvvnfbx3lSuS5cu4ZtvvsHo0aPx5JNP4tChQ3jzzTehUqkwaNAg+z1w9T7HcUfL19ixY5GVlYV+/fpBoVDAbDbjueeew3333QcAvDdepDj3Ijk5GSEhIbL9SqUSgYGBZfpex6AlERERERGRl5o5cyZOnTqFr7/+urKbQgCuXr2Kt956C8uWLYNGo6ns5tBNJElCmzZtMHnyZABAq1atcOrUKXz77bcYNBXIvjEAAA8qSURBVGhQJbeuZtu4cSPWr1+PuXPnolmzZjh27Bhmz56NWrVq8d6QWwxaEhGVQEJCAnr37i3bptVq4e/vj6ZNm6Jjx44YNGgQGjRoUEktJCIi8kxwcDAUCoXTpDspKSkICwurpFbVbLNmzcLff/+Nr776CnXq1LFvDwsLg9FoREZGhiyjLyUlBeHh4ZXR1BrjyJEjSElJweDBg+3bzGYzdu/ejVWrVmHp0qW8N5UoPDwcTZs2lW1r0qQJfvvtN/t+wHo/atWqZS+TkpKC6OjoimtoDfTuu+9i7Nix6N+/PwAgKioKV65cweLFizFo0CDeGy9SnHsRFhaG1NRU2XEmkwk3btwo0/c6jmlJRFQKDRo0wMSJEzFx4kSMGjUKt99+O1JTU/HJJ5+gb9+++OCDDyBJUmU3k4iIqEhqtRqtW7dGfHy8fZvFYkF8fDw6dOhQiS2reSRJwqxZs/DHH39gxYoVqF+/vmx/mzZtoFKpZPfq7NmzuHLlCtq3b1/Bra1Zbr31Vqxfvx4//fST/adNmzYYMGCAfZn3pvJ07NjRPmaizfnz51GvXj0AQGRkJMLDw2X3JysrCwcOHOD7XDnLy8uDIAiybQqFwv6sxHvjPYpzLzp06ICMjAwcPnzYXmbHjh2wWCyIiYkps7Yw05KIqBQaNGiASZMmOW3/77//MHXqVCxevBiiKOLZZ5+t+MYRERF5aPTo0Zg2bRratGmDmJgYrFixArm5ubKsMip/M2fOxM8//4xPPvkEfn5+9vHB/P397T07hgwZgjlz5iAwMBA6nQ5vvvkmOnTowMBYOdPpdPaxRW18fX0RFBRk3857U3keffRRPPTQQ1i0aBH69euHgwcPYvXq1Zg1axYAQBAEjBo1Cp9++ikaNmyIyMhIfPzxx6hVqxb69OlTya2v3nr27IlFixYhIiLC3j18+fLlGDJkCADem4qWnZ2Nixcv2tcTEhJw7NgxBAYGIiIiosh70bRpU3Tv3h2vvvoqZs6cCaPRiDfeeAP9+/dH7dq1y6ydgsQUICIij9m6h8fFxWHp0qUuy5w9exb3338/JEnCH3/8gbp16yIzMxPffPMNtm7divPnzyM9PR1BQUGIjY3FxIkTZd3JP/zwQyxatAgffvgh7rnnHqf6v//+e0yfPh2TJ0/GuHHjAFi7LC1evBgHDx5EcnIydDod6tWrhz59+uCpp54qn4tBRETVyldffYWlS5ciKSkJLVu2xCuvvIJ27dpVdrNqlKioKJfbZ8+ebQ8g6/V6zJkzB7/88gsMBgPi4uIwY8YMdkGuBI888giio6Mxffp0ALw3lW3z5s344IMPcP78eURGRmL06NEYNmyYfb8kSZg3bx5Wr16NjIwMdOrUCTNmzEDjxo0rsdXVX1ZWFj7++GNs2rTJ3u24f//+mDBhAtRqNQDem4q0c+dOjBo1ymn7oEGDMGfOnGLdi/T0dLzxxhv466+/IIoi7rrrLrzyyivw8/Mrs3YyaElEVALFCVoCwNSpU7Fu3Tq88soreOSRR7B//36MHDkSXbt2RYMGDeDj44OzZ89i69at8Pf3x48//mjvvnL58mX06dMHt912G5YtW+ZU9/Dhw3H48GH8/fffCA8Px7FjxzBs2DAoFAr07t0bERERyMjIwJkzZ3D58mVs3ry53K4HERERERERUVli93AionLUpUsXrFu3DocOHQJgTaP/559/EBQUJCu3Y8cOjB49Gp9++inefPNNAEC9evUQFxeHbdu2ISEhAZGRkfbyp06dwv79+9GnTx/7t/br1q2DwWDAwoULnbpQpKWlleOrJCIiIiIiIipbnIiHiKgc2WZbswUN/f39nQKWgHVQ92bNmmH79u2y7Q8++CAkScL3338v275mzRoAkHV1sdFqtU7bgoODS9R+IiIiIiIiosrAoCURUQXbuXMnxo8fj7i4OLRu3RpRUVGIiorCyZMnkZiYKCt7xx13oHbt2vjxxx9hNpsBAAaDAevWrUPdunXRvXt3e9l+/fpBFEVMnDgRL730En7++Wdcv369Ql8bERERERERUVlg93AionJkC0KGhIQAADZu3IjnnnsOvr6+iIuLQ7169eDj4wNBELB27VpcvnxZdrxCocDQoUOxYMECbN26FT179sSmTZuQnp6OkSNHQhQd3z21a9cOK1euxKJFi/Dzzz/jxx9/BAC0bdsWzz//PG699dYKetVEREREREREpcOgJRFROdq1axcAa+AQABYsWACNRoMff/wRjRo1kpX95ZdfXNYxdOhQfPrpp1i9ejV69uyJNWvWQBRFDBkyxKls586dsWTJEuTl5eHAgQPYvHkzvv76a4wbNw4///wz6tevX7YvkIiIiIiIiKgcsHs4EVE5OXfuHDZu3Ai1Wo0777wTAHDx4kU0bdrUKWCZmJiIhIQEl/XUqVMHPXr0wNatW7F3717Ex8cjLi4OERERbs+t1WrRtWtXvPjiixg3bhzy8vLw77//ltlrIyIiIiIiIipPDFoSEZWDPXv2YMyYMTAYDBg7dixq164NAIiIiMCFCxeQnJxsL6vX6/H666/DaDS6re/BBx+EyWTCM888A0mSXE7As2/fPuj1eqftKSkpAACNRlPal0VERERERERUIdg9nIioFC5evIj58+cDAIxGI1JSUnDw4EGcPHkSCoUCTz31FCZOnGgv/8gjj+CNN97AwIED0bdvX5hMJmzfvh2SJCE6OhrHjx93eZ7u3bujXr16uHz5MsLDw9GzZ0+nMp9//jl27tyJW265BZGRkVCr1Th69Cji4+NRv359e7YnERERERERkbdj0JKIqBQuXryIBQsWALB2yfb390eTJk0wfvx4DBo0CA0aNJCVHzFiBJRKJb766iusXr0aAQEB6NGjB6ZMmYJnnnnG7XlEUcR9992HTz/9FIMGDYJS6fz2/dBDD8Hf3x8HDhzArl27IEkSIiIi8OSTT+LRRx+FTqcr2xdPRERERFSIXr16AQD++uuvSm4JEVVFgiRJUmU3goiIijZu3Dhs2bIFv/32Gxo2bFjZzSEiIiKiCpCQkIDevXsXWqZevXpeGRhk0JKISoOZlkREVcDp06exZcsWdOvWjQFLIiIiohqoQYMGuO+++1zu8/f3r+DWEBGVPwYtiYi82Pr163Hu3Dn89NNPAIAJEyZUboOIiIiIqFI0aNAAkyZNquxmEBFVGAYtiYi82OrVq/Hff/8hIiICb731Fjp27FjZTSIiIiIiLxYVFYUuXbrgvffew7vvvot///0XeXl5aNmyJZ5++mnExsY6HZOamopPP/0Uf/75JxITE+Hv748uXbpgwoQJaNGihVN5g8GAr7/+GuvXr8fZs2cBAHXr1kX37t0xfvx4BAYGyspnZ2fjww8/xK+//or09HQ0btwYEyZMQN++fcvnIhBRtcAxLYmIiIiIiIi8lG1My7i4OCxdurTI8lFRUYiKikJmZiaCg4MRGxuL1NRUbNy4EXq9HvPmzUOfPn3s5VNTUzF8+HBcvHgRXbp0Qfv27ZGQkIDffvsNarUaS5YsQefOne3l8/LyMHr0aOzduxeNGjVC9+7doVKpcOHCBWzfvh3ffPMNWrZsCcA6pqXRaES9evVw48YNxMbGIjc3Fxs2bEBeXh6WLFmCuLi4sr9oRFQtMNOSiIiIiIiIyMtdvHgR8+fPd7mvXbt2uP322+3rJ06cwL333ov3338fgiAAAEaNGoUHHngAr776KuLi4qDVagEA7733Hi5evIhx48Zh8uTJ9jq2bNmCsWPH4uWXX8avv/4KURQBAB9//DH27t2L+++/H7Nnz4ZCobAfk5mZaS9nk5iYiLZt2+LLL7+EWq0GAAwYMACPPfYYli9fzqAlEbnFoCURERERERGRl7t48SIWLFjgct+oUaNkQUuFQoHJkyfbA5YAEB0djfvvvx/ff/89tmzZgrvvvhsGgwG//PILgoKC8NRTT8nq7NGjB7p164Z///0Xe/fuRefOnWEymfDdd9/B398f06dPlwUsAfcTAr300kv2gCUA3HbbbahXrx4OHz7s8XUgoppDLLoIEREREREREVWmuLg4nDhxwuXP9OnTZWXr1q2LevXqOdVh6+Z99OhRAMDZs2eh1+sRExMDHx8fp/Jdu3YFABw7dsxePjs7G23btnUat9KdgIAA1K9f32l77dq1kZGRUaw6iKhmYtCSiIiIiIiIqBoJCwtzuT00NBQAkJWVJfvXXfnw8HBZuczMTADWgGNxucu+VCqVsFgsxa6HiGoeBi2JiIiIiIiIqpHk5GSX21NSUgAAOp1O9q+78rbttnIBAQEAgOvXr5ddY4mI3GDQkoiIiIiIiKgauXr1Ki5fvuy0/b///gMAtGrVCgDQpEkTaDQaHDp0CLm5uU7ld+7cCQD22cAbN24MnU6HQ4cO4caNG+XVfCIiAAxaEhEREREREVUrZrMZH3zwASRJsm87fvw41q1bh5CQEPTo0QMAoFar0b9/f6SlpWHx4sWyOrZu3Yp//vkHDRs2RMeOHQFYu3QPHz4cmZmZeOutt2A2m2XHZGZmIjs7u5xfHRHVFJw9nIiIiIiIiMjLXbx4EfPnz3e7f+zYsdBoNACAqKgo7N27F0OGDEFsbCxSU1OxceNGmM1mvPHGG9BqtfbjXnjhBezevRuffvop9u3bh3bt2uHy5cv49ddf4ePjg7fffhui6Mh3euaZZ3DgwAGsW7cOBw4cQPfu3aFWq5GQkIBt27bh66+/tmdmEhGVBoOWRERERERERF7u4sWLWLBggdv9jz76qD1oGRgYiM8++wzvvPMO1qxZg9zcXLRq1QqTJk1Ct27dZMeFhIRg9erV+OSTT/DXX39hz5490Ol06N27NyZOnIgWLVrIyms0GixfvhxfffUV/ve//2HNmjUQRRERERF48MEHXc5aTkRUEoJUMF+ciIiIiIiIiKqsqKgodOnSBStXrqzsphARlQrHtCQiIiIiIiIiIiKvwqAlEREREREREREReRUGLYmIiIiIiIiIiMircExLIiIiIiIiIiIi8irMtCQiIiIiIiIiIiKvwqAlEREREREREREReRUGLYmIiIiIiIiIiMirMGhJREREREREREREXoVBSyIiIiIiIiIiIvIqDFoSERERERERERGRV2HQkoiIiIiIiIiIiLwKg5ZERERERERERETkVf4fAdKdizsvD1IAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "sns.set_style(\"darkgrid\")    \n", "\n", "fig = plt.figure()\n", "fig.subplots_adjust(hspace=0.2, wspace=0.2)\n", "\n", "plt.subplot(1, 2, 1)\n", "ax = sns.lineplot(x = original.index, y = original[0], label=\"Data\", color='royalblue')\n", "ax = sns.lineplot(x = predict.index, y = predict[0], label=\"Training Prediction (LSTM)\", color='tomato')\n", "ax.set_title('Stock price', size = 14, fontweight='bold')\n", "ax.set_xlabel(\"Days\", size = 14)\n", "ax.set_ylabel(\"Cost (USD)\", size = 14)\n", "ax.set_xticklabels('', size=10)\n", "\n", "\n", "plt.subplot(1, 2, 2)\n", "ax = sns.lineplot(data=hist, color='royalblue')\n", "ax.set_xlabel(\"Epoch\", size = 14)\n", "ax.set_ylabel(\"Loss\", size = 14)\n", "ax.set_title(\"Training Loss\", size = 14, fontweight='bold')\n", "fig.set_figheight(6)\n", "fig.set_figwidth(16)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}