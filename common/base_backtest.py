"""
通用回测基类
提供滚动训练验证的通用逻辑，由具体的回测类继承实现
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 导入公共配置
from .globals import BacktestConfig, get_model_info, create_model_instance

# 导入字体配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from utils.font_config import setup_chinese_font
except ImportError:
    def setup_chinese_font():
        """字体配置函数的占位符"""
        pass

# 设置中文字体
setup_chinese_font()


class BaseBacktest(ABC):
    """回测基类"""
    
    def __init__(self, config: BacktestConfig):
        """
        初始化回测器
        
        Args:
            config: 回测配置
        """
        self.config = config
        self.model = None
        self.model_info = None
        
        # 创建保存目录
        os.makedirs(self.config.model_save_dir, exist_ok=True)
        os.makedirs(self.config.results_save_dir, exist_ok=True)
    
    def setup_model(self, model_name: str, **model_params):
        """
        设置模型
        
        Args:
            model_name: 模型名称
            **model_params: 模型参数
        """
        self.model_info = get_model_info(model_name)
        
        # 验证模型类型是否匹配
        expected_type = self.get_expected_model_type()
        expected_data_type = self.get_expected_data_type()
        
        if self.model_info['type'] != expected_type:
            raise ValueError(f"模型类型不匹配。期望: {expected_type}, 实际: {self.model_info['type']}")
        
        if self.model_info['data_type'] != expected_data_type:
            raise ValueError(f"数据类型不匹配。期望: {expected_data_type}, 实际: {self.model_info['data_type']}")
        
        # 创建模型实例
        self.model = create_model_instance(model_name, **model_params)
        print(f"✅ 模型设置完成: {model_name}")
        print(f"   类型: {self.model_info['type']}")
        print(f"   数据类型: {self.model_info['data_type']}")
    
    @abstractmethod
    def get_expected_model_type(self) -> str:
        """返回期望的模型类型 ('classification' 或 'regression')"""
        pass
    
    @abstractmethod
    def get_expected_data_type(self) -> str:
        """返回期望的数据类型 ('ml' 或 'dl')"""
        pass
    
    @abstractmethod
    def prepare_data(self, csv_file: str) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备数据（由子类实现具体的数据处理逻辑）
        
        Args:
            csv_file: 数据文件路径
            
        Returns:
            X: 特征数据
            y: 目标数据
            dates: 日期列表
        """
        pass
    
    @abstractmethod
    def evaluate_predictions(self, y_true, y_pred, **kwargs) -> Dict[str, float]:
        """
        评估预测结果（由子类实现具体的评估逻辑）
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            **kwargs: 其他参数
            
        Returns:
            评估指标字典
        """
        pass
    
    def run_backtest(self, model_name: str, csv_file: Optional[str] = None, 
                    **model_params) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            model_name: 模型名称
            csv_file: 数据文件路径（可选，使用配置中的默认值）
            **model_params: 模型参数
            
        Returns:
            回测结果
        """
        print("=" * 60)
        print(f"🚀 开始回测: {model_name}")
        print("=" * 60)
        
        # 设置模型
        self.setup_model(model_name, **model_params)
        
        # 使用配置中的文件路径
        data_file = csv_file or self.config.csv_file
        print(f"📊 数据文件: {data_file}")
        
        # 生成模型保存路径
        model_path = os.path.join(
            self.config.model_save_dir, 
            f"{model_name}_{os.path.basename(data_file).split('.')[0]}.pkl"
        )
        
        # 检查是否存在已保存的模型
        if os.path.exists(model_path):
            print(f"🔍 发现已保存的模型: {model_path}")
            return self._run_with_existing_model(model_path, data_file)
        else:
            print("🏋️ 未找到已保存的模型，开始滚动训练验证...")
            return self._run_rolling_backtest(model_path, data_file)
    
    def _run_with_existing_model(self, model_path: str, data_file: str) -> Dict[str, Any]:
        """使用已存在的模型进行测试"""
        # 加载模型
        self.model.load_model(model_path)
        
        # 准备数据
        print("📈 准备数据进行测试...")
        X, y, dates = self.prepare_data(data_file)
        
        # 使用最后一部分数据进行测试
        test_size = min(1000, len(X) // 4)
        X_test = X[-test_size:]
        y_test = y[-test_size:]
        test_dates = dates[-test_size:]
        
        print(f"🧪 使用最后 {test_size} 个样本进行测试...")
        
        # 预测
        y_pred = self.model.predict(X_test)
        
        # 评估
        metrics = self.evaluate_predictions(y_test, y_pred)
        
        print("📊 测试结果:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                print(f"   {metric}: {value:.4f}")
            else:
                print(f"   {metric}: {value}")
        
        return {
            'type': 'existing_model_test',
            'metrics': metrics,
            'predictions': y_pred,
            'true_values': y_test,
            'dates': test_dates
        }
    
    def _run_rolling_backtest(self, model_path: str, data_file: str) -> Dict[str, Any]:
        """运行滚动回测"""
        # 准备数据
        print("📈 准备数据...")
        X, y, dates = self.prepare_data(data_file)
        
        # 滚动训练验证
        print("🔄 开始滚动训练验证...")
        results = self.model.rolling_train_validate(X, y, dates)
        
        # 保存模型
        print("💾 保存模型...")
        self.model.save_model(model_path)
        
        # 保存和可视化结果
        if self.config.save_detailed_results:
            self._save_results(results, model_path)
        
        if self.config.plot_results:
            self._plot_results(results, model_path)
        
        return results
    
    def _save_results(self, results: Dict[str, Any], model_path: str):
        """保存详细结果"""
        base_name = os.path.splitext(os.path.basename(model_path))[0]
        results_file = os.path.join(self.config.results_save_dir, f"{base_name}_results.csv")
        
        if 'validation_results' in results:
            results_df = pd.DataFrame(results['validation_results'])
            results_df.to_csv(results_file, index=False)
            print(f"📁 详细结果已保存到: {results_file}")
    
    def _plot_results(self, results: Dict[str, Any], model_path: str):
        """绘制结果图表"""
        if hasattr(self.model, 'plot_results'):
            base_name = os.path.splitext(os.path.basename(model_path))[0]
            plot_file = os.path.join(self.config.results_save_dir, f"{base_name}_plot.png")
            self.model.plot_results(results, save_path=plot_file)
            print(f"📈 结果图表已保存到: {plot_file}")
    
    def get_feature_importance(self, top_n: int = 20) -> Optional[pd.DataFrame]:
        """获取特征重要性（如果模型支持）"""
        if hasattr(self.model, 'get_feature_importance'):
            return self.model.get_feature_importance(top_n)
        else:
            print("⚠️ 当前模型不支持特征重要性分析")
            return None
    
    def print_summary(self, results: Dict[str, Any]):
        """打印回测总结"""
        print("\n" + "=" * 60)
        print("📋 回测总结")
        print("=" * 60)
        
        if 'final_metrics' in results:
            print("🎯 最终指标:")
            for metric, value in results['final_metrics'].items():
                if isinstance(value, float):
                    print(f"   {metric}: {value:.4f}")
                else:
                    print(f"   {metric}: {value}")
        
        # 显示特征重要性
        feature_imp = self.get_feature_importance(top_n=10)
        if feature_imp is not None:
            print("\n🔍 特征重要性 (Top 10):")
            print(feature_imp.to_string(index=False))
