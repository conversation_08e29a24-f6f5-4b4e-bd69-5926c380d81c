import numpy as np

def rolling_normalize(data, window=200):
	"""
	使用滚动窗口进行标准化，避免未来数据泄漏
	跳过前期数据不足的样本，确保标准化质量

	Args:
		data: numpy array, shape [时间步数, 特征数]
		window: int, 滚动窗口大小

	Returns:
		normalized_data: 标准化后的数据（从第window个样本开始）
		valid_indices: 有效数据的索引范围
	"""
	# 只处理有足够历史数据的样本
	valid_start = window - 1  # 从第window个样本开始（索引为window-1）
	valid_data = data[valid_start:]
	normalized = np.zeros_like(valid_data)

	for i in range(len(valid_data)):
		# 当前在原数据中的位置
		current_idx = valid_start + i
		# 用过去window天的数据计算统计量
		train_data = data[current_idx-window+1:current_idx+1]
		# 计算均值和标准差
		mean = np.mean(train_data, axis=0)
		std = np.std(train_data, axis=0)
		# 避免标准差为0的情况
		std = np.where(std == 0, 1, std)
		# 标准化当前时间点的数据
		normalized[i] = (valid_data[i] - mean) / std
	return normalized, valid_start