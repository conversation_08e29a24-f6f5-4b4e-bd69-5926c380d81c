import numpy as np
import pandas as pd



def detect_fvg_signals(df, lookback=20):
	"""检测FVG交易信号"""
	n = len(df)
	fvg_bull_signal = np.zeros(n)
	fvg_bear_signal = np.zeros(n)

	for i in range(lookback + 2, n):
		current_close = df['close'].iloc[i]

		# 检查过去lookback个周期内的FVG
		for j in range(i - lookback, i - 2):
			if j < 2:
				continue

			# 检测看涨FVG (第j-2根K线的high < 第j根K线的low)
			if df['high'].iloc[j-2] < df['low'].iloc[j]:
				fvg_top = df['low'].iloc[j]
				fvg_bottom = df['high'].iloc[j-2]

				# 当前价格是否进入FVG区域且发生回调
				if fvg_bottom <= current_close <= fvg_top:
					# 检查是否有回调确认（最近3根K线有下跌后反弹）
					recent_lows = df['low'].iloc[i-2:i+1]
					if len(recent_lows) >= 2 and recent_lows.min() < current_close:
						fvg_bull_signal[i] = 1
						break

			# 检测看跌FVG (第j-2根K线的low > 第j根K线的high)
			if df['low'].iloc[j-2] > df['high'].iloc[j]:
				fvg_top = df['low'].iloc[j-2]
				fvg_bottom = df['high'].iloc[j]

				# 当前价格是否进入FVG区域且发生回调
				if fvg_bottom <= current_close <= fvg_top:
					# 检查是否有回调确认（最近3根K线有上涨后回落）
					recent_highs = df['high'].iloc[i-2:i+1]
					if len(recent_highs) >= 2 and recent_highs.max() > current_close:
						fvg_bear_signal[i] = 1
						break

	return fvg_bull_signal, fvg_bear_signal


def engineer_features_X(df: pd.DataFrame) -> pd.DataFrame:
    features = pd.DataFrame(index=df.index)

    # ===== 振幅与收盘位置 =====
    features['price_range'] = (df['high'] - df['low']) / df['close']
    high_low_diff = df['high'] - df['low']
    features['price_position'] = np.where(high_low_diff == 0, 1,
                                          (df['close'] - df['low']) / high_low_diff)

    # ===== 多周期均线比 =====
    for period_s, period_l in [(5, 20), (20, 60), (20, 200)]:
        ma_s = df['close'].rolling(period_s).mean()
        ma_l = df['close'].rolling(period_l).mean()
        features[f'ma_ratio_{period_s}_{period_l}'] = ma_s / ma_l

    # ===== 成交量特征（多周期量比） =====
    for period in [5, 20, 200]:
        volume_ma = df['volume'].rolling(period).mean()
        features[f'volume_ratio_{period}'] = np.where(volume_ma == 0, 1, df['volume'] / volume_ma)

    # ===== VWAP（多周期） =====
    typical_price = (df['high'] + df['low'] + df['close']) / 3
    for period in [5, 20, 200]:
        pv_sum = (typical_price * df['volume']).rolling(period).sum()
        volume_sum = df['volume'].rolling(period).sum()
        vwap = pv_sum / volume_sum
        features[f'vwap_distance_{period}'] = (df['close'] - vwap) / df['close']

    # ===== 波动率特征（多周期） =====
    for period in [5, 20, 200]:
        features[f'volatility_{period}'] = df['close'].rolling(period).std()

    # ===== RSI（多周期） =====
    delta = df['close'].diff().astype(float)
    for period in [5, 20, 200]:
        gain = (delta.where(delta > 0, 0)).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        rs = np.where(loss == 0, 100, gain / loss)
        features[f'rsi_{period}'] = 100 - (100 / (1 + rs))

    # ===== 突破类特征 =====
    for period in [5, 20, 200]:
        features[f'is_new_high_{period}'] = (df['close'] > df['high'].shift(1).rolling(period).max()).astype(int)
        features[f'is_new_low_{period}'] = (df['close'] < df['low'].shift(1).rolling(period).min()).astype(int)

    # ===== K线形态特征 =====
    features['upper_shadow'] = (df['high'] - np.maximum(df['close'], df['open'])) / df['close']
    features['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / df['close']
    features['close_open_range'] = (df['close'] - df['open']) / df['close']

    # ===== 连续上涨/下跌（多周期） =====
    for period in [5, 20, 200]:
        features[f'up_k_count_{period}'] = (df['close'] > df['close'].shift(1)).rolling(period).sum()
        features[f'down_k_count_{period}'] = (df['close'] < df['close'].shift(1)).rolling(period).sum()

    # ===== 换手率特征 =====
    for period in [5, 20, 200]:
        turnover_ma = df['turnover'].rolling(period).mean()
        features[f'turnover_ratio_{period}'] = np.where(turnover_ma == 0, 1, df['turnover'] / turnover_ma)

    # ===== ATR波动特征（多周期） =====
    true_range = np.maximum(df['high'] - df['low'],
                            np.maximum(abs(df['high'] - df['close'].shift(1)),
                                       abs(df['low'] - df['close'].shift(1))))
    true_range_series = pd.Series(true_range, index=df.index)
    for period in [5, 20, 200]:
        features[f'atr_{period}'] = true_range_series.rolling(period).mean()

	# ===== FVG公平价值缺口交易信号 =====
    fvg_bull, fvg_bear = detect_fvg_signals(df)
    features['fvg_bull_signal'] = fvg_bull
    features['fvg_bear_signal'] = fvg_bear

    return features