"""
全局配置和模型注册表
统一管理所有回测相关的配置和模型注册
"""

import os
import sys
from dataclasses import dataclass
from typing import Dict, Any, Optional, Type, Union
from abc import ABC, abstractmethod

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@dataclass
class BacktestConfig:
    """回测配置类"""
    # 数据配置
    csv_file: str = './k_data/AAPL_US_5min.csv'
    
    # 训练配置
    batch_size: int = 500
    validation_size: int = 100
    window: int = 200  # 滚动标准化窗口
    
    # 模型保存路径
    model_save_dir: str = './models_saved'
    
    # 结果保存路径
    results_save_dir: str = './results'
    
    # 是否平衡类别（仅分类任务）
    balance_classes: bool = True
    
    # 是否保存详细结果
    save_detailed_results: bool = True
    
    # 是否绘制结果图表
    plot_results: bool = True


class BaseModel(ABC):
    """模型基类接口"""
    
    @abstractmethod
    def prepare_data(self, csv_file: str, **kwargs):
        """准备数据"""
        pass
    
    @abstractmethod
    def train_model(self, X_train, y_train, **kwargs):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict(self, X):
        """预测"""
        pass
    
    @abstractmethod
    def rolling_train_validate(self, X, y, dates):
        """滚动训练验证"""
        pass
    
    @abstractmethod
    def save_model(self, path: str):
        """保存模型"""
        pass
    
    @abstractmethod
    def load_model(self, path: str):
        """加载模型"""
        pass


# 机器学习模型注册表
ML_MODELS: Dict[str, Dict[str, Any]] = {
    'lgbm_classify': {
        'module': 'models.lightgbm.lgbm_classify',
        'class': 'LightGBMStockClassificationModel',
        'type': 'classification',
        'data_type': 'ml',  # 机器学习：2D数据 (batch_size, features)
        'default_params': {
            'num_leaves': 31,
            'learning_rate': 0.05,
            'n_estimators': 100,
            'random_state': 42
        }
    },
    'lgbm_regression': {
        'module': 'models.lightgbm.lgbm_regression',
        'class': 'LightGBMStockRegressionModel',
        'type': 'regression',
        'data_type': 'ml',
        'default_params': {
            'num_leaves': 31,
            'learning_rate': 0.05,
            'n_estimators': 100,
            'random_state': 42
        }
    }
}

# 深度学习模型注册表
DL_MODELS: Dict[str, Dict[str, Any]] = {
    'transformer_classify': {
        'module': 'models.trm.trmV2',
        'class': 'TradeTransformer',
        'type': 'classification',
        'data_type': 'dl',  # 深度学习：3D数据 (batch_size, seq_len, features)
        'default_params': {
            'input_dim': None,  # 运行时确定
            'pos_dim': 16,
            'model_dim': 64,
            'nhead': 4,
            'num_layers': 2,
            'num_classes': 3,
            'max_seq_len': 200
        }
    },
    'lstm_classify': {
        'module': 'models.lstm.lstmV8',  # 假设使用最新版本
        'class': 'LSTM',
        'type': 'classification',
        'data_type': 'dl',
        'default_params': {
            'input_dim': None,  # 运行时确定
            'hidden_dim': 64,
            'num_layers': 2,
            'output_dim': 3
        }
    }
}

# 合并所有模型注册表
ALL_MODELS = {**ML_MODELS, **DL_MODELS}


def get_model_info(model_name: str) -> Dict[str, Any]:
    """
    获取模型信息
    
    Args:
        model_name: 模型名称
        
    Returns:
        模型信息字典
        
    Raises:
        ValueError: 如果模型不存在
    """
    if model_name not in ALL_MODELS:
        available_models = list(ALL_MODELS.keys())
        raise ValueError(f"模型 '{model_name}' 不存在。可用模型: {available_models}")
    
    return ALL_MODELS[model_name]


def get_models_by_type(model_type: str) -> Dict[str, Dict[str, Any]]:
    """
    根据类型获取模型列表
    
    Args:
        model_type: 'classification' 或 'regression'
        
    Returns:
        符合类型的模型字典
    """
    return {name: info for name, info in ALL_MODELS.items() 
            if info['type'] == model_type}


def get_models_by_data_type(data_type: str) -> Dict[str, Dict[str, Any]]:
    """
    根据数据类型获取模型列表
    
    Args:
        data_type: 'ml' 或 'dl'
        
    Returns:
        符合数据类型的模型字典
    """
    return {name: info for name, info in ALL_MODELS.items() 
            if info['data_type'] == data_type}


def create_model_instance(model_name: str, **kwargs):
    """
    创建模型实例
    
    Args:
        model_name: 模型名称
        **kwargs: 模型参数
        
    Returns:
        模型实例
    """
    model_info = get_model_info(model_name)
    
    # 动态导入模块
    module_name = model_info['module']
    class_name = model_info['class']
    
    module = __import__(module_name, fromlist=[class_name])
    model_class = getattr(module, class_name)
    
    # 合并默认参数和用户参数
    params = model_info['default_params'].copy()
    params.update(kwargs)
    
    # 创建实例
    return model_class(**params)


# 评估指标配置
CLASSIFICATION_METRICS = ['accuracy', 'precision', 'recall', 'f1', 'confusion_matrix']
REGRESSION_METRICS = ['mse', 'mae', 'rmse', 'r2', 'direction_accuracy']

# 文件路径配置
DEFAULT_PATHS = {
    'data_dir': './k_data',
    'model_dir': './models_saved',
    'results_dir': './results',
    'plots_dir': './plots'
}
