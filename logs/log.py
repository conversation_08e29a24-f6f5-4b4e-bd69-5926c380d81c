from loguru import logger
from datetime import datetime
import pytz

# 设置中国时区
china = pytz.timezone('Asia/Shanghai')

# 自定义时间格式化函数
def format_time(record):
    # 将UTC时间转换为中国时区
    china_time = record["time"].astimezone(china)
    record["time"] = china_time
    return True

# 设置日志格式（使用中国时区）
fmt = '{time:MM-DD HH:mm:ss}|{level}|{message}'
cur_date = datetime.now(tz=china).strftime('%Y-%m-%d')

logger.add(f'logs/{cur_date}.log',
	level='INFO',
	format=fmt,
	filter=format_time,
	rotation="1 day",
	retention=3)
logger.info('='*20 + ' Start ' + '='*20)