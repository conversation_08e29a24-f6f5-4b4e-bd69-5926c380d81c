"""
深度学习分类模型回测
支持 Transformer、LSTM 等深度学习分类模型的滚动回测
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import torch
from typing import Dict, Any, List, Tuple
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.base_backtest import BaseBacktest
from common.globals import BacktestConfig, get_models_by_type, get_models_by_data_type


class DLClassifyBacktest(BaseBacktest):
    """深度学习分类回测类"""
    
    def get_expected_model_type(self) -> str:
        return 'classification'
    
    def get_expected_data_type(self) -> str:
        return 'dl'
    
    def prepare_data(self, csv_file: str) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备深度学习分类数据
        
        Args:
            csv_file: 数据文件路径
            
        Returns:
            X: 特征数据 [样本数, seq_len, 特征数]
            y: 目标标签 [样本数]
            dates: 日期列表
        """
        print("📊 准备深度学习分类数据...")
        
        # 导入深度学习数据准备函数
        from models.trm.trmV2 import prepare_stock_data
        
        # 准备数据（这里使用固定的seq_len，可以根据需要调整）
        seq_len = 200
        X, y, input_dim, dates = prepare_stock_data(
            csv_file, 
            seq_len=seq_len
        )
        
        print(f"✅ 数据准备完成:")
        print(f"   特征形状: {X.shape}")
        print(f"   标签形状: {y.shape}")
        print(f"   输入维度: {input_dim}")
        print(f"   日期数量: {len(dates)}")
        
        # 统计类别分布
        unique, counts = np.unique(y, return_counts=True)
        class_names = ['跌', '平', '涨']
        print(f"   类别分布:")
        for label, count in zip(unique, counts):
            pct = count / len(y) * 100
            print(f"     类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")
        
        return X, y, dates
    
    def evaluate_predictions(self, y_true, y_pred, **kwargs) -> Dict[str, float]:
        """
        评估分类预测结果
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签或概率
            **kwargs: 其他参数
            
        Returns:
            评估指标字典
        """
        # 处理PyTorch张量
        if torch.is_tensor(y_true):
            y_true = y_true.cpu().numpy()
        if torch.is_tensor(y_pred):
            y_pred = y_pred.cpu().numpy()
        
        # 如果预测结果是概率，取最大概率对应的类别
        if y_pred.ndim > 1:
            y_pred = np.argmax(y_pred, axis=1)
        
        # 计算基本指标
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
        f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
        
        # 计算混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        # 计算各类别准确率
        class_accuracies = {}
        class_names = ['跌', '平', '涨']
        for i, class_name in enumerate(class_names):
            if i < len(cm):
                class_total = np.sum(cm[i, :])
                class_correct = cm[i, i] if class_total > 0 else 0
                class_acc = class_correct / class_total if class_total > 0 else 0
                class_accuracies[f'{class_name}_accuracy'] = class_acc
        
        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            **class_accuracies
        }
        
        return metrics
    
    def print_confusion_matrix(self, y_true, y_pred):
        """打印混淆矩阵"""
        # 处理PyTorch张量
        if torch.is_tensor(y_true):
            y_true = y_true.cpu().numpy()
        if torch.is_tensor(y_pred):
            y_pred = y_pred.cpu().numpy()
            
        if y_pred.ndim > 1:
            y_pred = np.argmax(y_pred, axis=1)
        
        cm = confusion_matrix(y_true, y_pred)
        class_names = ['跌', '平', '涨']
        
        print("\n📊 混淆矩阵:")
        print("     预测")
        print("   ", end="")
        for name in class_names:
            print(f"{name:>6}", end="")
        print()
        
        for i, true_name in enumerate(class_names):
            print(f"{true_name:>2} ", end="")
            for j in range(len(class_names)):
                if i < len(cm) and j < len(cm[i]):
                    print(f"{cm[i, j]:>6}", end="")
                else:
                    print(f"{'0':>6}", end="")
            print()
    
    def setup_model(self, model_name: str, **model_params):
        """
        设置深度学习模型（重写以处理设备和特殊参数）
        
        Args:
            model_name: 模型名称
            **model_params: 模型参数
        """
        # 检查GPU可用性
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 使用设备: {device}")
        
        # 调用父类方法设置模型
        super().setup_model(model_name, **model_params)
        
        # 如果是PyTorch模型，移动到设备
        if hasattr(self.model, 'to'):
            self.model = self.model.to(device)
            print(f"📱 模型已移动到: {device}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='深度学习分类模型回测')
    parser.add_argument('--model', type=str, required=True, 
                       help='模型名称 (如: transformer_classify, lstm_classify)')
    parser.add_argument('--data', type=str, 
                       help='数据文件路径')
    parser.add_argument('--batch_size', type=int, default=160,
                       help='训练批次大小')
    parser.add_argument('--seq_len', type=int, default=200,
                       help='序列长度')
    parser.add_argument('--epochs', type=int, default=50,
                       help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='学习率')
    parser.add_argument('--no_plot', action='store_true',
                       help='不绘制结果图表')
    parser.add_argument('--no_save', action='store_true',
                       help='不保存详细结果')
    
    args = parser.parse_args()
    
    # 验证模型是否支持
    dl_classify_models = get_models_by_type('classification')
    dl_classify_models = {k: v for k, v in dl_classify_models.items() 
                         if v['data_type'] == 'dl'}
    
    if args.model not in dl_classify_models:
        print(f"❌ 不支持的模型: {args.model}")
        print(f"支持的深度学习分类模型: {list(dl_classify_models.keys())}")
        return
    
    # 创建配置
    config = BacktestConfig(
        csv_file=args.data or './k_data/AAPL_US_5min.csv',
        batch_size=args.batch_size,
        validation_size=100,  # 深度学习通常使用较小的验证集
        window=200,
        balance_classes=True,
        plot_results=not args.no_plot,
        save_detailed_results=not args.no_save
    )
    
    # 创建回测器
    backtest = DLClassifyBacktest(config)
    
    try:
        # 准备模型参数
        model_params = {
            'max_seq_len': args.seq_len,
            'learning_rate': args.learning_rate,
            'epochs': args.epochs
        }
        
        # 运行回测
        results = backtest.run_backtest(args.model, **model_params)
        
        # 打印总结
        backtest.print_summary(results)
        
        # 如果有预测结果，打印混淆矩阵
        if 'predictions' in results and 'true_values' in results:
            backtest.print_confusion_matrix(results['true_values'], results['predictions'])
        
        print("\n✅ 回测完成!")
        
    except Exception as e:
        print(f"❌ 回测失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
