# type: ignore
import sys, os
# 把项目根目录添加到系统路径中
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
print(sys.path)
from datetime import datetime
import pytz
import exchange_calendars as ecals
import pandas as pd
from decimal import Decimal
from logs.log import logger
from longport.openapi import Config, QuoteContext, CalcIndex, Period, TradeContext, \
	OrderType, OrderSide, TimeInForceType, AdjustType, OutsideRTH
    
from dotenv import load_dotenv

DIRT_SHORT = 0  # 空头仓位
DIRT_LONG = 1  # 多头仓位

# 加载环境变量
load_dotenv()
# 初始化行情和交易ctx
config = Config.from_env()
trade_ctx = TradeContext(config)
query_ctx = QuoteContext(config)
print(f'当前账户余额: {trade_ctx.account_balance()}')


def get_kline_data(symbol: str, period: Period, end_dt: datetime, count: int) -> pd.DataFrame:
    resp = query_ctx.history_candlesticks_by_offset(symbol, period,
        AdjustType.ForwardAdjust, forward=False, count=count, time=end_dt)
    if not resp:
        return pd.DataFrame()

    data = [
        {
            "open": c.open,
            "high": c.high,
            "low": c.low,
            "close": c.close,
            "volume": c.volume,
            "turnover": c.turnover,
            "timestamp": c.timestamp,
        }
        for c in resp
    ]
    df = pd.DataFrame(data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df['open'] = df['open'].astype(float)
    df['high'] = df['high'].astype(float)
    df['low'] = df['low'].astype(float)
    df['close'] = df['close'].astype(float)
    df['volume'] = df['volume'].astype(float)
    df['turnover'] = df['turnover'].astype(float)
    df.set_index('timestamp', inplace=True)
    return df


def get_kline_data_batch(symbol: str, period: Period, start_dt: datetime, end_dt: datetime, batch_size: int = 1000) -> pd.DataFrame:
    """
    批量获取K线数据，突破单次1000条的限制

    Args:
        symbol: 股票代码，如 'TQQQ.US'
        period: 时间周期，如 Period.Min_5
        start_dt: 开始时间
        end_dt: 结束时间
        batch_size: 每批获取的数量，默认1000

    Returns:
        合并后的完整DataFrame
    """
    all_data = []
    current_end = end_dt

    logger.info(f"开始批量获取 {symbol} 的K线数据，从 {start_dt} 到 {end_dt}")

    batch_count = 0
    while True:
        batch_count += 1
        logger.info(f"正在获取第 {batch_count} 批数据，截止时间: {current_end}")

        # 获取当前批次的数据
        batch_df = get_kline_data(symbol, period, current_end, batch_size)

        if batch_df.empty:
            logger.warning(f"第 {batch_count} 批数据为空，停止获取")
            break

        # 过滤出在指定时间范围内的数据
        batch_df = batch_df[batch_df.index >= start_dt]

        if batch_df.empty:
            logger.info(f"第 {batch_count} 批数据已超出起始时间范围，停止获取")
            break

        all_data.append(batch_df)
        logger.info(f"第 {batch_count} 批获取了 {len(batch_df)} 条数据")

        # 更新下一批的结束时间为当前批次最早的时间
        current_end = batch_df.index.min()

        # 如果当前批次的最早时间已经达到或超过起始时间，停止获取
        if current_end <= start_dt:
            logger.info(f"已获取到起始时间 {start_dt}，停止获取")
            break

        # 如果当前批次数据量小于batch_size，说明已经获取完所有数据
        if len(batch_df) < batch_size:
            logger.info(f"当前批次数据量 {len(batch_df)} 小于批次大小 {batch_size}，已获取完所有数据")
            break

    if not all_data:
        logger.warning("未获取到任何数据")
        return pd.DataFrame()

    # 合并所有数据
    combined_df = pd.concat(all_data, axis=0)

    # 去重并排序
    combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
    combined_df = combined_df.sort_index()

    # 再次过滤确保在时间范围内
    combined_df = combined_df[(combined_df.index >= start_dt) & (combined_df.index <= end_dt)]

    logger.info(f"批量获取完成！总共获取了 {len(combined_df)} 条数据")
    return combined_df


def download_kline_batch(symbol: str, period: Period, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:
    print(f"开始获取 {symbol} 从 {start_time} 到 {end_time} 的5分钟K线数据...")

    # 使用批量获取函数，可以获取超过1000条的数据
    df_batch = get_kline_data_batch(symbol, period, start_time, end_time)
    print(f"批量获取完成！共获取 {len(df_batch)} 条数据")
    print(df_batch.head())
    print("...")
    print(df_batch.tail())

    # 保存到CSV文件
    if not df_batch.empty:
        filename = f"{symbol.replace('.', '_')}_{start_time.strftime('%Y%m%d')}_{end_time.strftime('%Y%m%d')}_5min.csv"
        df_batch.to_csv(f"k_data/{filename}")
        print(f"数据已保存到: k_data/{filename}")

    # 对比：单次获取只能获取1000条
    print("\n对比：单次获取最多1000条数据")
    df_single = get_kline_data(symbol, Period.Min_5, end_time, 1000)
    print(f"单次获取了 {len(df_single)} 条数据")
    print(f"时间范围: {df_single.index.min()} 到 {df_single.index.max()}")




if __name__ == '__main__':
    symbol = 'TQQQ.US' # 注意这里下的时间都是中国的时间, 只有盘中数据
    # start_time = datetime(2023, 12, 4)
    # end_time = datetime(2025, 1, 1)
    start_time = datetime(2025, 1, 1)
    end_time = datetime(2025, 7, 1)

    download_kline_batch(symbol, Period.Min_5, start_time, end_time)

