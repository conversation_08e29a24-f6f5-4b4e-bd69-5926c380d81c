# type: ignore
"""
特征重要性分析工具
分析哪个特征对股票涨跌相关性影响最大

主要功能：
1. 计算特征与涨跌的相关性
2. 使用多种方法分析特征重要性（相关性、互信息、随机森林、XGBoost、LightGBM）
3. 可视化特征重要性排名
4. 生成详细的分析报告
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
from sklearn.feature_selection import mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 导入特征工程函数
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from models.trm.trmV2 import engineer_features_X
from utils.font_config import setup_chinese_font

# 设置中文字体
setup_chinese_font()


class FeatureImportanceAnalyzer:
    """特征重要性分析器"""
    
    def __init__(self, csv_file: str):
        """
        初始化分析器
        
        Args:
            csv_file: 股票数据CSV文件路径
        """
        self.csv_file = csv_file
        self.df = None
        self.features_df = None
        self.target = None
        self.feature_names = None
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("加载数据...")
        
        # 1. 读取原始数据
        self.df = pd.read_csv(self.csv_file)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        self.df = self.df.sort_values('datetime').reset_index(drop=True)
        print(f"原始数据形状: {self.df.shape}")
        
        # 2. 特征工程
        print("进行特征工程...")
        self.features_df = engineer_features_X(self.df)
        print(f"特征工程后形状: {self.features_df.shape}")
        
        # 3. 创建目标变量（未来3日平均涨跌）
        print("创建目标变量...")
        future_return_1d = self.df['close'].shift(-1) / self.df['close'] - 1
        future_return_2d = self.df['close'].shift(-2) / self.df['close'] - 1  
        future_return_3d = self.df['close'].shift(-3) / self.df['close'] - 1
        future_return_avg = (future_return_1d + future_return_2d + future_return_3d) / 3
        
        # 使用阈值分类
        threshold = 0.001
        def assign_label(return_val):
            if pd.isna(return_val):
                return np.nan
            elif return_val <= -threshold:
                return 0  # 跌
            elif return_val >= threshold:
                return 2  # 涨  
            else:
                return 1  # 平
                
        self.target = future_return_avg.apply(assign_label)
        
        # 4. 合并特征和目标，删除NaN
        combined_df = pd.concat([self.features_df, self.target.rename('target')], axis=1)
        combined_df = combined_df.dropna()
        
        self.features_df = combined_df.iloc[:, :-1]
        self.target = combined_df['target'].astype(int)
        self.feature_names = list(self.features_df.columns)
        
        print(f"最终数据形状: 特征={self.features_df.shape}, 目标={self.target.shape}")
        print(f"目标分布: {self.target.value_counts().sort_index().to_dict()}")
        
    def analyze_correlation(self):
        """分析特征与目标的相关性"""
        print("\n=== 相关性分析 ===")
        
        correlations = []
        for feature in self.feature_names:
            # Pearson相关系数
            pearson_corr, pearson_p = pearsonr(self.features_df[feature], self.target)
            
            # Spearman相关系数  
            spearman_corr, spearman_p = spearmanr(self.features_df[feature], self.target)
            
            correlations.append({
                'feature': feature,
                'pearson_corr': abs(pearson_corr),
                'pearson_p': pearson_p,
                'spearman_corr': abs(spearman_corr), 
                'spearman_p': spearman_p
            })
            
        corr_df = pd.DataFrame(correlations)
        corr_df = corr_df.sort_values('pearson_corr', ascending=False)
        
        self.results['correlation'] = corr_df
        print("Pearson相关性Top 10:")
        print(corr_df[['feature', 'pearson_corr', 'pearson_p']].head(10))
        
    def analyze_mutual_information(self):
        """分析互信息"""
        print("\n=== 互信息分析 ===")
        
        # 计算互信息
        mi_scores = mutual_info_classif(self.features_df, self.target, random_state=42)
        
        mi_df = pd.DataFrame({
            'feature': self.feature_names,
            'mutual_info': mi_scores
        }).sort_values('mutual_info', ascending=False)
        
        self.results['mutual_info'] = mi_df
        print("互信息Top 10:")
        print(mi_df.head(10))
        
    def analyze_random_forest(self):
        """使用随机森林分析特征重要性"""
        print("\n=== 随机森林特征重要性 ===")
        
        rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(self.features_df, self.target)
        
        rf_importance = pd.DataFrame({
            'feature': self.feature_names,
            'rf_importance': rf.feature_importances_
        }).sort_values('rf_importance', ascending=False)
        
        self.results['random_forest'] = rf_importance
        print("随机森林重要性Top 10:")
        print(rf_importance.head(10))
        
    def analyze_xgboost(self):
        """使用XGBoost分析特征重要性"""
        print("\n=== XGBoost特征重要性 ===")
        
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            random_state=42,
            eval_metric='mlogloss'
        )
        xgb_model.fit(self.features_df, self.target)
        
        xgb_importance = pd.DataFrame({
            'feature': self.feature_names,
            'xgb_importance': xgb_model.feature_importances_
        }).sort_values('xgb_importance', ascending=False)
        
        self.results['xgboost'] = xgb_importance
        print("XGBoost重要性Top 10:")
        print(xgb_importance.head(10))
        
    def analyze_lightgbm(self):
        """使用LightGBM分析特征重要性"""
        print("\n=== LightGBM特征重要性 ===")
        
        lgb_model = lgb.LGBMClassifier(
            n_estimators=100,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(self.features_df, self.target)
        
        lgb_importance = pd.DataFrame({
            'feature': self.feature_names,
            'lgb_importance': lgb_model.feature_importances_
        }).sort_values('lgb_importance', ascending=False)
        
        self.results['lightgbm'] = lgb_importance
        print("LightGBM重要性Top 10:")
        print(lgb_importance.head(10))
        
    def create_comprehensive_ranking(self):
        """创建综合排名"""
        print("\n=== 综合排名分析 ===")
        
        # 合并所有结果
        ranking_df = pd.DataFrame({'feature': self.feature_names})
        
        # 添加各种重要性指标的排名
        for method, result_df in self.results.items():
            if method == 'correlation':
                ranking_df = ranking_df.merge(
                    result_df[['feature', 'pearson_corr']].reset_index().rename(columns={'index': f'{method}_rank'}),
                    on='feature'
                )
            else:
                score_col = result_df.columns[1]  # 第二列是分数
                ranking_df = ranking_df.merge(
                    result_df[['feature', score_col]].reset_index().rename(columns={'index': f'{method}_rank'}),
                    on='feature'
                )
        
        # 计算平均排名
        rank_cols = [col for col in ranking_df.columns if col.endswith('_rank')]
        ranking_df['avg_rank'] = ranking_df[rank_cols].mean(axis=1)
        ranking_df = ranking_df.sort_values('avg_rank')
        
        self.results['comprehensive_ranking'] = ranking_df
        print("综合排名Top 15:")
        print(ranking_df[['feature', 'avg_rank'] + rank_cols].head(15))
        
        return ranking_df
        
    def plot_feature_importance(self, save_path: str = './feature_importance_analysis.png'):
        """绘制特征重要性图表"""
        print(f"\n绘制特征重要性图表，保存到: {save_path}")
        
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('特征重要性分析', fontsize=16, fontweight='bold')
        
        # 1. 相关性
        top_corr = self.results['correlation'].head(10)
        axes[0, 0].barh(range(len(top_corr)), top_corr['pearson_corr'])
        axes[0, 0].set_yticks(range(len(top_corr)))
        axes[0, 0].set_yticklabels(top_corr['feature'])
        axes[0, 0].set_title('Pearson相关性')
        axes[0, 0].set_xlabel('相关系数绝对值')
        
        # 2. 互信息
        top_mi = self.results['mutual_info'].head(10)
        axes[0, 1].barh(range(len(top_mi)), top_mi['mutual_info'])
        axes[0, 1].set_yticks(range(len(top_mi)))
        axes[0, 1].set_yticklabels(top_mi['feature'])
        axes[0, 1].set_title('互信息')
        axes[0, 1].set_xlabel('互信息分数')
        
        # 3. 随机森林
        top_rf = self.results['random_forest'].head(10)
        axes[0, 2].barh(range(len(top_rf)), top_rf['rf_importance'])
        axes[0, 2].set_yticks(range(len(top_rf)))
        axes[0, 2].set_yticklabels(top_rf['feature'])
        axes[0, 2].set_title('随机森林')
        axes[0, 2].set_xlabel('重要性分数')
        
        # 4. XGBoost
        top_xgb = self.results['xgboost'].head(10)
        axes[1, 0].barh(range(len(top_xgb)), top_xgb['xgb_importance'])
        axes[1, 0].set_yticks(range(len(top_xgb)))
        axes[1, 0].set_yticklabels(top_xgb['feature'])
        axes[1, 0].set_title('XGBoost')
        axes[1, 0].set_xlabel('重要性分数')
        
        # 5. LightGBM
        top_lgb = self.results['lightgbm'].head(10)
        axes[1, 1].barh(range(len(top_lgb)), top_lgb['lgb_importance'])
        axes[1, 1].set_yticks(range(len(top_lgb)))
        axes[1, 1].set_yticklabels(top_lgb['feature'])
        axes[1, 1].set_title('LightGBM')
        axes[1, 1].set_xlabel('重要性分数')
        
        # 6. 综合排名
        top_comprehensive = self.results['comprehensive_ranking'].head(10)
        axes[1, 2].barh(range(len(top_comprehensive)), 1/(top_comprehensive['avg_rank']+1))
        axes[1, 2].set_yticks(range(len(top_comprehensive)))
        axes[1, 2].set_yticklabels(top_comprehensive['feature'])
        axes[1, 2].set_title('综合排名')
        axes[1, 2].set_xlabel('综合分数')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
    def run_full_analysis(self):
        """运行完整分析"""
        print("开始特征重要性分析...")

        # 1. 加载数据
        self.load_and_prepare_data()

        # 2. 各种分析方法
        self.analyze_correlation()
        self.analyze_mutual_information()
        self.analyze_random_forest()
        self.analyze_xgboost()
        self.analyze_lightgbm()

        # 3. 综合排名
        comprehensive_ranking = self.create_comprehensive_ranking()

        # 4. 特征分组分析
        self.analyze_feature_groups()

        # 5. 生成详细报告
        self.generate_detailed_report()

        # 6. 绘图
        self.plot_feature_importance()

        # 7. 保存结果
        self.save_results()

        return comprehensive_ranking
        
    def analyze_feature_groups(self):
        """分析特征组的重要性"""
        print("\n=== 特征分组分析 ===")

        # 定义特征组
        feature_groups = {
            '价格位置': ['price_range', 'price_position'],
            '均线比率': [col for col in self.feature_names if 'ma_ratio' in col], 
            '成交量': [col for col in self.feature_names if 'volume_ratio' in col],
            'RSI指标': [col for col in self.feature_names if 'rsi' in col],
            'K线形态': ['upper_shadow', 'lower_shadow', 'close_open_range'],
            '连续涨跌': [col for col in self.feature_names if 'up_k_count' in col or 'down_k_count' in col],
            '换手率': [col for col in self.feature_names if 'turnover_ratio' in col],
            'ATR波动': [col for col in self.feature_names if 'atr' in col],
            'FVG信号': [col for col in self.feature_names if 'fvg' in col],
            '新高新低': [col for col in self.feature_names if 'new_high' in col or 'new_low' in col]
        }

        # 计算每个特征组的平均重要性
        group_importance = {}
        comprehensive_ranking = self.results['comprehensive_ranking']

        for group_name, features in feature_groups.items():
            group_features = [f for f in features if f in self.feature_names]
            if group_features:
                group_ranks = comprehensive_ranking[comprehensive_ranking['feature'].isin(group_features)]['avg_rank']
                group_importance[group_name] = {
                    'avg_rank': group_ranks.mean(),
                    'best_rank': group_ranks.min(),
                    'feature_count': len(group_features),
                    'best_feature': comprehensive_ranking[comprehensive_ranking['feature'].isin(group_features)].iloc[0]['feature']
                }

        group_df = pd.DataFrame(group_importance).T.sort_values('avg_rank')
        self.results['feature_groups'] = group_df

        print("特征组重要性排名:")
        print(group_df)

    def generate_detailed_report(self):
        """生成详细的分析报告"""
        print("\n=== 详细分析报告 ===")

        comprehensive_ranking = self.results['comprehensive_ranking']

        print("📊 数据概况:")
        print(f"  - 总样本数: {len(self.target)}")
        print(f"  - 特征数量: {len(self.feature_names)}")
        print(f"  - 目标分布: 跌({(self.target==0).sum()}) 平({(self.target==1).sum()}) 涨({(self.target==2).sum()})")

        print("\n🏆 最重要的5个特征:")
        top5 = comprehensive_ranking.head(5)
        for i, (_, row) in enumerate(top5.iterrows(), 1):
            feature = row['feature']
            avg_rank = row['avg_rank']

            # 获取该特征的详细统计
            feature_data = self.features_df[feature]
            corr_with_target = abs(np.corrcoef(feature_data, self.target)[0, 1])

            print(f"  {i}. {feature}")
            print(f"     - 综合排名: {avg_rank:.2f}")
            print(f"     - 与目标相关性: {corr_with_target:.4f}")
            print(f"     - 数据范围: [{feature_data.min():.4f}, {feature_data.max():.4f}]")

        print("\n📈 各方法一致性分析:")
        rank_cols = [col for col in comprehensive_ranking.columns if col.endswith('_rank')]
        consistency_scores = []

        for _, row in comprehensive_ranking.head(10).iterrows():
            ranks = [row[col] for col in rank_cols]
            consistency = 1 / (np.std(ranks) + 1)  # 标准差越小，一致性越高
            consistency_scores.append(consistency)

        avg_consistency = np.mean(consistency_scores)
        print(f"  - Top10特征的方法一致性分数: {avg_consistency:.3f}")

        if 'feature_groups' in self.results:
            print("\n🎯 特征组分析:")
            group_df = self.results['feature_groups']
            for group_name, row in group_df.head(3).iterrows():
                print(f"  - {group_name}: 平均排名{row['avg_rank']:.1f}, 最佳特征: {row['best_feature']}")

    def save_results(self, output_dir: str = './'):
        """保存分析结果"""
        print(f"\n保存分析结果到: {output_dir}")

        # 保存各种分析结果
        for method, result_df in self.results.items():
            filename = f"{output_dir}feature_importance_{method}.csv"
            result_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"已保存: {filename}")


def main():
    """主函数 - 集成了文件选择和分析功能"""
    print("🚀 股票特征重要性分析工具")
    print("=" * 50)

    # 数据文件路径
    data_files = [
        "k_data/TQQQ_US_5min.csv",
        "k_data/NVDA_US_5min.csv",
        "k_data/AAPL_US_5min.csv",
        "k_data/AMZN_US_5min.csv",
    ]

    # 查找存在的数据文件
    available_files = [f for f in data_files if os.path.exists(f)]

    if not available_files:
        print("❌ 未找到数据文件，请检查以下路径:")
        for file_path in data_files:
            print(f"   - {file_path}")
        return

    print(f"📁 找到 {len(available_files)} 个数据文件:")
    for i, file_path in enumerate(available_files, 1):
        print(f"   {i}. {file_path}")

    # 选择要分析的文件
    if len(available_files) == 1:
        selected_file = available_files[0]
        print(f"\n🎯 自动选择: {selected_file}")
    else:
        print(f"\n请选择要分析的文件 (1-{len(available_files)}):")
        try:
            choice = int(input("输入序号: ")) - 1
            if 0 <= choice < len(available_files):
                selected_file = available_files[choice]
            else:
                print("❌ 无效选择，使用第一个文件")
                selected_file = available_files[0]
        except ValueError:
            print("❌ 输入无效，使用第一个文件")
            selected_file = available_files[0]

    print(f"🔍 开始分析文件: {selected_file}")
    print("=" * 50)

    try:
        # 创建分析器并运行分析
        analyzer = FeatureImportanceAnalyzer(selected_file)
        comprehensive_ranking = analyzer.run_full_analysis()

        print("\n" + "=" * 50)
        print("✅ 分析完成！")
        print("\n🏆 最重要的15个特征:")
        print("-" * 40)

        top_features = comprehensive_ranking.head(15)
        for i, (_, row) in enumerate(top_features.iterrows(), 1):
            feature = row['feature']
            avg_rank = row['avg_rank']
            print(f"{i:2d}. {feature:<25} (排名: {avg_rank:.2f})")

        print("\n📊 输出文件:")
        print("   - feature_importance_analysis.png  (可视化图表)")
        print("   - feature_importance_*.csv        (详细数据)")

        print("\n💡 使用建议:")
        print("   1. 查看生成的PNG图表了解各方法的特征重要性")
        print("   2. 重点关注综合排名前10的特征")
        print("   3. 考虑特征组的整体表现")
        print("   4. 结合业务逻辑验证特征的合理性")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        print("请检查数据文件格式是否正确")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
